ARG SMI_FROM_LATEST_MINOR=dockerhub.cisco.com/smi-fuse-docker-internal/smi-libraries/releases/ubuntu-base/22.04.5/ubuntu-base:22.04.5
FROM ${SMI_FROM_LATEST_MINOR}

RUN mkdir /artifacts
COPY ./artifacts/inception-install.iso /artifacts/
COPY ./artifacts/inception-install.part.tar.xz /artifacts/
COPY ./artifacts/smi_base_initrd.img /artifacts/
COPY ./base-image/vmlinuz-5.4.0-128-generic /artifacts/
RUN ls
