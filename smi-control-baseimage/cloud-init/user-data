#cloud-config
debug: true
output:
  all: "| tee -a /var/log/cloud-init-output.log"
manage_etc_hosts: false
resize_rootfs: false
final_message: "Cloud-init completed after $UPTIME seconds"
apt:
  preserve_sources_list: true
users:
  - name: "cloud-user"
    sudo: ["ALL=(ALL) NOPASSWD:ALL"]
    shell: /bin/bash
    groups: docker
    
chpasswd:
  list: |
    cloud-user:Cisco_123
  expire: true

write_files: 
  - 
    content: "alias smi-config='docker run -v /var/run/docker.sock:/var/run/docker.sock -v /data:/data -v /home:/home -v /etc/environment:/etc/environment --network=host smi-config:build-image smi-config'\n"
    owner: "root:root"
    path: /etc/profile.d/smi-aliases.sh
    permissions: "0755"

bootcmd:
  - sed -i 's|PasswordAuthentication no|PasswordAuthentication yes|g' /etc/ssh/sshd_config
  - sed -i 's/ro $TUNED/ro  $TUNED/g' /etc/grub.d/40_custom
  - sed -i 's/^.*source/        source/g' /etc/grub.d/40_custom
  - chage -E -1 tac
  - ln -sf /run/systemd/resolve/resolv.conf /etc/resolv.conf
