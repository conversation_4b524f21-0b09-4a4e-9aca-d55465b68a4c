- name: restart_chrony
  systemd:
    state: restarted
    daemon_reload: yes
    name: chrony

- name: restart_sshd
  service:
    name: ssh
    state: restarted

- name: force_time_sync
  register: sync_result
  until: sync_result.rc == 0
  retries: 30
  delay: 1
  shell: | 
      set -e
      chronyc -a online
      chronyc -a burst 2/2
      chronyc -a tracking | grep -q 'System time.*slow' 
      chronyc -a makestep

- name: verify_chrony_status
  #Leap status could be Normal(stable), Insert second, Delete second or Not synchronised
  shell: "chronyc tracking | grep -i 'Leap status     : Normal'"
  retries: 30
  register: chrony_status
  until: chrony_status.stdout.find("Normal") != -1

- name: check_system_time
  register: task_result
  until: task_result.rc == 0
  retries: 10
  shell: |
          set -o pipefail
          ul=20
          ll=-20
          result=$(chronyc tracking | grep -i "System time" | cut -d: -f2 | awk -F' ' '{print $1}' | awk -F'system' '{print $1}')
          tmp=$(echo $result*1000 | bc)
          if [[ $(echo "$tmp < $ul && $tmp > $ll" | bc) -eq 1 ]]; then 
            echo "$ll < $tmp < $ul"
          else 
            echo "Time is not properly synced."
            exit -1
          fi
          
- name: restart_auditd
  service:
    name: auditd
    state: restarted

- name: reload_daemon
  systemd:
    daemon_reload: yes
