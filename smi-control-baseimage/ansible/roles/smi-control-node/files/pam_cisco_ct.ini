# PAM Cisco Consent Token (PCCT) config file

[Demo section]
# This line is displayed at login, before the challenge is issued.
login_announce = Cisco TAC Consent Token authentication

# This is the Cisco root of trust key (TLV format, base64 encoded)
cisco_root_tlv_key = rgIpqxI0zQEAAQICAAEBAwEJBAEAxi3HI2Tgp98maiMsJMmmvW2nxpiFJ8dk6ZrFgNJ5XFa7NPMYVbFpz0BSa97Hkju7zshQrPZZV2S+SmnxYeGflh53HcHIKeXyJRgCcasK5XRdP5K2YkPZ90Q33WEh5X8rKnSVWz6RtG19XGI8mlr77qnsHjX4PyWD2ZTR44ZWLAPW+mV+BcwlGbGTyi6O1O5guqfAp+IJKAXhQYcxeETdBeYAoFWlOmExwibEdADkjlaeQTTSftpFv8EjTQaBzyhwnRvD00K0VYKaKzNr+ckxbZ0jWbDbJd+b3M4bqe8CoLiikm65EsXKg7ex/updgzXqeaWEdDHPvnEXMH6I76M+5wUAAwEAAQYAAUEHAAhDVEFfREVNTwgBAAhILFTuCmJFyN0l5xa1TxXpeiPETG2+lqcAtCK7ZFRcFkGEoVNsykNzvkGEif2n9gJjMvKfnDbpYj96+kv5V9uf681uo+busTh7oOYvn3xRWLi+x+sqkgcDf5kcd46PPMltXmzpPOLzuF/9+LxQLpuXXphR2AIxAYx8arlOM4ffX8o6kkJRZ5NtV1ktjrdqbBGDPXCBuVsw6EWpWFBrNBBY8nPIhW9FjCbKCortPSeuXsiY/5+9TC0VBnPk3YoSoLlnmXZjaK9F44cQSK6mGEcH9p7ycVh8xtJTeA6FOcArTF0NudabmpnJbJt9y2ykjEvLmiPPK0NRcaChXm/Tq+3r6+u+78r+

# SWIMS product, key, and PID values
product_name = 5GaaS
product_key_name = 5GAAS_CT_KEY
product_pid = P5G-MGMT

# If this value is specified and not empty, it will be used.
# Otherwise, /sys/devices/virtual/dmi/id/chassis_serial will be used.
#product_serial_number =

# This is your product's RELEASE image signing public certificate
# (x.509 format, DER encoded, base64 encoded)
product_imgsign_rel_x509_cert = MIIDzzCCAregAwIBAgIJAMu/obAfwBTEMA0GCSqGSIb3DQEBDQUAMC8xDjAMBgNVBAoTBUNpc2NvMR0wGwYDVQQDExRJbm5lcnNwYWNlIFN1YkNBIFJTQTAeFw0yMjAxMTMxOTIwNDVaFw0zNzExMTExOTIwNDVaMC4xDjAMBgNVBAMMBTVHYWFTMQwwCgYDVQQLDANSRUwxDjAMBgNVBAoMBUNpc2NvMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0umnH1nk7RNieSsFvfg7pwbs6A15dPkHT0CbWFXvSIf920OVh+Lwtf3aw30CAOfj62T4wtxL9DL3B/SWIGEg02mwup/EZjhFwQ6BYH3kXIS0MS+EimND7R6kjMK85LDdvTYGWflLofo79aW3GcLd+mvPW4cKMVYybqKA78YuJITigRq/SBGa7UGihjDi9ts//w/KigeB99y0vVL+7XdpMofUmA1aZfvfaUIhIpnm/WmpFzrMly05E3Sd25CBAzti6YROHLBDudRBECq32nYQ4u2rmKiftTgE5EyXas0IeeWI8C8xazUltsN1++m6Fc+B85DCWChaRKgg+HAfhga/9wIDAQABo4HuMIHrMA4GA1UdDwEB/wQEAwIHgDAMBgNVHRMBAf8EAjAAMB0GA1UdDgQWBBQbsAQKfYtNLcEM78PzRQ4OZwa+cjBFBgNVHR8EPjA8MDqgOKA2hjRodHRwOi8vd3d3LmNpc2NvLmNvbS9zZWN1cml0eS9wa2kvY3JsL2lubmVyc3BhY2UuY3JsMEQGCCsGAQUFBwEBBDgwNjA0BggrBgEFBQcwAYYoaHR0cDovL3Rvb2xzLmNpc2NvLmNvbS9wa2kvc2VydmljZXMvb2NzcDAfBgNVHSMEGDAWgBRZR/BsOVuQ98hLrvMUwkcio8uQLzANBgkqhkiG9w0BAQ0FAAOCAQEAaw3F7RcGHYCkh6+981rn+SCt5BYCLDQptXmPDF+AkUj3Xr1Ix6Wefux14jCQPuOR/3Qsy+5/PhCELonzHu6xBobz40MbL3sSGPFsilLGCQq2rAcsOnpropzb7OJo2nzaMGC8LHyObGWo3eXUpxs1sVP04bpM8MTswRHMqp2cjIwugZTR77q4Vpn0JGByT7MAF5j+gE6I4JGDZWrE8q0+bQtaVxY04MXpxrC3U1iIAP5D9grZhGB26ikL3KRQXEPUaRo9E02xuV4+hWWB3eHfRGrGCcQoCB+kLxmhJn2SCj4mxpdW7eq9xQEAp6NAHoZOEbERYA/2hsGW1PGqEyttng==
# This is your product's DEV consent token signing public certificate
# (x.509 format, DER encoded, base64 encoded)
product_ct_signing_x509_cert = MIID6DCCAtCgAwIBAgIJAMrmfmmDq/uBMA0GCSqGSIb3DQEBDQUAMC4xDjAMBgNVBAMMBTVHYWFTMQwwCgYDVQQLDANSRUwxDjAMBgNVBAoMBUNpc2NvMB4XDTIyMDEyMDE4Mjg1M1oXDTM5MDExNjE4Mjg1M1owNTEVMBMGA1UEAwwMNUdBQVNfQ1RfS0VZMQwwCgYDVQQLDANERVYxDjAMBgNVBAoMBUNpc2NvMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxRCvMWQcAsvlDi9nZhn8IUBfNnwq1cbGFMlWwXxFf8BVQWDRtjVoQDUfA/zu/XGAELusmTZ1Sg8qQJKI7OT4BrTPaGm4x8NciQVQv2aT7xVCfX7P4xD+Z6op0zYXJFkY+OltH2esM1uURYwjYNVY7A6ws1X7FHHZ3npdMzYBEk7NUnyv2mY4purcmw9Fdom93rqpvPAaxrmVDrthPRPKM3W44CvcDraGLchLd7kq7rEg8p+2HbCOq4WJSPNedhjzVS9U3g86jTmA7K0hLYHRaFgKwvS4ddcUC/P12DgtU0tzKQWM4iIKIY1ZXpOtuokjYvuMTd4jJV0jyXRlhMGL5QIDAQABo4IBADCB/TAMBgNVHRMBAf8EAjAAMA4GA1UdDwEB/wQEAwIHgDAdBgNVHQ4EFgQUwcbdDnOrWVMXa6gIS9nvBpSjR/swXwYDVR0jBFgwVoAUG7AECn2LTS3BDO/D80UODmcGvnKhM6QxMC8xDjAMBgNVBAoTBUNpc2NvMR0wGwYDVQQDExRJbm5lcnNwYWNlIFN1YkNBIFJTQYIJAMu/obAfwBTEMF0GA1UdIARWMFQwUgYLKwYBBAEJFQEkAQYwQzBBBggrBgEFBQcCARY1aHR0cDovL3d3dy5jaXNjby5jb20vc2VjdXJpdHkvcGtpL3BvbGljaWVzL2luZGV4Lmh0bWwwDQYJKoZIhvcNAQENBQADggEBANKffQfNYaB4BCKfFF3aLNwJEsy6S9w7N5hIm5fC1NABu1eTPzn8YHVR1OqmUGI2ispDAZkZiY5rAMHJ9DgpTGybQn8pPkdAtbltUQCVzY/3HIyuqvr0CrVkUi7EW98DhRMwZ1L546YwS/Lh6wwfqTocxXIdQ162/c5ROHZcjl2vNZpHwcax/JEI7Dw328iQeZxHvA/a3vWsNq90je/YyDSn+fcouFppuEINL4MV/2gBz2X/rinN/ddAb8hLwTx+dkpLaz3yOa0nVWoDAIyChl40bQDLCPzKuZ5H2Imutu5hHl/XQ0zybZgdKhZSJSVerlOLjR83eBKFNzk7uwRnXrM=
# This is a comma-delimited list of local Linux users that you want to
# use this consent token configuration.
users = tac

# If you have a SWIMS ticket for automatically generating a consent
# token, enter it here.  This field is ignored if the PCCT is not
# built with SWIMS ticket support.
#ticket =

# The default URL for ticket signing is the SWIMS production server.
# (i.e., https://swims.cisco.com/aberto/api/sign).  You can list a
# different URL (e.g., the SWIMS staging server), if necessary.
#ticket_url = https://swims-stg.cisco.com/aberto/api/sign