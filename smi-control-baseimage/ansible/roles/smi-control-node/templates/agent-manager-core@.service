[Unit]
Description=%i agent
PartOf=network.target cloud-init.service
After=network.target cloud-init.service containerd.service

[Service]
Type=oneshot
RemainAfterExit=true
User=cloud-user
Group=docker
EnvironmentFile=/etc/environment
Environment=TMPDIR='/data/control-node/run/'
WorkingDirectory=/data/control-node/agents/core/%i
ExecStart=/usr/local/bin/docker-compose up -d --remove-orphans
ExecReload=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutSec=60

[Install]
WantedBy=multi-user.target
