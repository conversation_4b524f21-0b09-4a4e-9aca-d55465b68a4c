## First rule - delete all
-D

## Increase the buffers to survive stress events.
## Make this bigger for busy systems
-b 8192

## This determine how long to wait in burst of events
--backlog_wait_time 0

## Set failure mode to syslog
-f 1

# Ignore errors
## e.g. caused by users or files not found in the local environment  
-i

# date and time modification logging
-a always,exit -F arch=b64 -S adjtimex -S settimeofday -k time-change
-a always,exit -F arch=b32 -S adjtimex -S settimeofday -S stime -k time-change
-a always,exit -F arch=b64 -S clock_settime -k time-change
-a always,exit -F arch=b32 -S clock_settime -k time-change
-w /etc/localtime -p wa -k time-change

# user/group modification logging
-w /etc/group -p wa -k identity
-w /etc/passwd -p wa -k identity
-w /etc/gshadow -p wa -k identity
-w /etc/shadow -p wa -k identity
-w /etc/security/opasswd -p wa -k identity

# docker daemon, service, containerd logging
-w /usr/bin/dockerd -p wa
-w /usr/bin/docker -p wa
-w /var/lib/docker -p wa
-w /etc/docker -p wa
-w /lib/systemd/system/docker.service -p wa
-w /lib/systemd/system/docker.socket -p wa
-w /etc/default/docker -p wa
-w /etc/docker/daemon.json -p wa
-w /usr/bin/containerd -p wa
-w /usr/bin/runc -p wa

# network environment modification logging
-a always,exit -F arch=b64 -S sethostname -S setdomainname -k system-locale 
-a always,exit -F arch=b32 -S sethostname -S setdomainname -k system-locale 
-w /etc/issue -p wa -k system-locale
-w /etc/issue.net -p wa -k system-locale
-w /etc/hosts -p wa -k system-locale
-w /etc/network -p wa -k system-locale
-w /etc/networks -p wa -k system-locale

# Mandatory access control modification logging
-w /etc/selinux/ -p wa -k MAC-policy
-w /etc/apparmor/ -p wa -k MAC-policy
-w /etc/apparmor.d/ -p wa -k MAC-policy

# login/logout events logging
-w /var/log/faillog -p wa -k logins
-w /var/log/lastlog -p wa -k logins
-w /var/log/tallylog -p wa -k logins

# Session initiation information logging
-w /var/run/utmp -p wa -k session
-w /var/log/wtmp -p wa -k session
-w /var/log/btmp -p wa -k session

# access control permission modification logging
-a always,exit -F arch=b64 -S chmod -S fchmod -S fchmodat -F auid>=1000 -F auid!=********** -k perm_mod 
-a always,exit -F arch=b32 -S chmod -S fchmod -S fchmodat -F auid>=1000 -F auid!=********** -k perm_mod 
-a always,exit -F arch=b64 -S chown -S fchown -S fchownat -S lchown -F auid>=1000 -F auid!=********** -k perm_mod 
-a always,exit -F arch=b32 -S chown -S fchown -S fchownat -S lchown -F auid>=1000 -F auid!=********** -k perm_mod 
-a always,exit -F arch=b64 -S setxattr -S lsetxattr -S fsetxattr -S removexattr -S lremovexattr -S fremovexattr -F auid>=1000 -F auid!=********** -k perm_mod 
-a always,exit -F arch=b32 -S setxattr -S lsetxattr -S fsetxattr -S removexattr -S lremovexattr -S fremovexattr -F auid>=1000 -F auid!=********** -k perm_mod

# unauthorized file access logging
-a always,exit -F arch=b64 -S creat -S open -S openat -S truncate -S ftruncate -F exit=-EACCES -F auid>=1000 -F auid!=********** -k access 
-a always,exit -F arch=b32 -S creat -S open -S openat -S truncate -S ftruncate -F exit=-EACCES -F auid>=1000 -F auid!=********** -k access 
-a always,exit -F arch=b64 -S creat -S open -S openat -S truncate -S ftruncate -F exit=-EPERM -F auid>=1000 -F auid!=********** -k access 
-a always,exit -F arch=b32 -S creat -S open -S openat -S truncate -S ftruncate -F exit=-EPERM -F auid>=1000 -F auid!=********** -k access

# file system mount logs
-a always,exit -F arch=b64 -S mount -F auid>=1000 -F auid!=********** -k mounts 
-a always,exit -F arch=b32 -S mount -F auid>=1000 -F auid!=********** -k mounts

# privileged commands logs
-a always,exit -F path=/bin/su -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/bin/mount -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/bin/ping -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/bin/ping6 -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/bin/fusermount -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/bin/umount -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/sbin/unix_chkpwd -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/ssh-agent -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/chfn -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/chage -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/mlocate -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/passwd -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/mail-touchlock -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/at -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/sudo -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/mtr -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/dotlockfile -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/bsd-write -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/screen -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/crontab -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/wall -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/chsh -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/pkexec -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/expiry -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/gpasswd -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/newgrp -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/mail-unlock -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/traceroute6.iputils -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/bin/mail-lock -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/sbin/pppd -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/sbin/uuidd -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/lib/eject/dmcrypt-get-device -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/lib/dbus-1.0/dbus-daemon-launch-helper -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/lib/openssh/ssh-keysign -F perm=x -F auid>=1000 -F auid!=********** -k privileged
-a always,exit -F path=/usr/lib/pt_chown -F perm=x -F auid>=1000 -F auid!=********** -k privileged

# file deletion event logs
-a always,exit -F arch=b64 -S unlink -S unlinkat -S rename -S renameat -F auid>=1000 -F auid!=********** -k delete 
-a always,exit -F arch=b32 -S unlink -S unlinkat -S rename -S renameat -F auid>=1000 -F auid!=********** -k delete

# su scope logs
-w /etc/sudoers -p wa -k scope
-w /etc/sudoers.d -p wa -k scope

# su logs
-w /var/log/sudo.log -p wa -k actions

# Kernal module loading/unloading logs
-w /sbin/insmod -p x -k modules
-w /sbin/rmmod -p x -k modules
-w /sbin/modprobe -p x -k modules
-a always,exit -F arch=b64 -S init_module -S delete_module -k modules

# Self Auditing ---------------------------------------------------------------

## Audit the audit logs
### Successful and unsuccessful attempts to read information from the audit records
-w /var/log/audit/ -k auditlog

## Auditd configuration
### Modifications to audit configuration that occur while the audit collection functions are operating
-w /etc/audit/ -p wa -k auditconfig
-w /etc/libaudit.conf -p wa -k auditconfig
-w /etc/audisp/ -p wa -k audispconfig

## Monitor for use of audit management tools
-w /sbin/auditctl -p x -k audittools
-w /sbin/auditd -p x -k audittools

# Filters ---------------------------------------------------------------------

### We put these early because audit is a first match wins system.
# Added the following lines to reduce the log size 
# Added by Watts on 4-8-2019
#-a exit,exclude -F arch=b64 -F path=/lib64/ld-linux-x86-64.so.2
-a exclude,always -F msgtype=/lib64/ld-linux-x86-64.so.2 -F ext=-2
#Possible fix to remove ping noise.
#Added by Paul Farag on 5/6/2019
#The following lines will need to be tested in order to remove health checks from showing up in the logs:
#-a exit,never a0="ping"
#-a exit,never a0="/bin/ping"

## Ignore SELinux AVC records
-a always,exclude -F msgtype=AVC

## Ignore current working directory records
-a always,exclude -F msgtype=CWD

## Ignore EOE records (End Of Event, not needed)
-a always,exclude -F msgtype=EOE

## Cron jobs fill the logs with stuff we normally don't want (works with SELinux)
-a never,user -F subj_type=crond_t
-a exit,never -F subj_type=crond_t

## This prevents chrony from overwhelming the logs
##-a never,exit -F arch=b64 -S adjtimex -F auid=unset -F uid=chrony -F subj_type=chronyd_t

## This is not very interesting and wastes a lot of space if the server is public facing
-a always,exclude -F msgtype=CRYPTO_KEY_USER

### High Volume Event Filter (especially on Linux Workstations)
-a exit,never -F arch=b32 -F dir=/dev/shm -k sharedmemaccess
-a exit,never -F arch=b64 -F dir=/dev/shm -k sharedmemaccess
-a exit,never -F arch=b32 -F dir=/var/lock/lvm -k locklvm
-a exit,never -F arch=b64 -F dir=/var/lock/lvm -k locklvm


# Rules -----------------------------------------------------------------------
# Adding instance numbers for easier troubleshooting. Using _[0-9] at the end of the comment line
#Added by Paul Farag on 3-21-19
## Kernel Related Events
-w /etc/sysctl.conf -p wa -k sysctl
-a always,exit -F perm=x -F auid!=-1 -F path=/sbin/insmod -k T1215_Kernel_Modules_and_Extensions_1
-a always,exit -F perm=x -F auid!=-1 -F path=/sbin/modprobe -k T1215_Kernel_Modules_and_Extensions_2
-a always,exit -F perm=x -F auid!=-1 -F path=/sbin/rmmod -k T1215_Kernel_Modules_and_Extensions_3
-a always,exit -F arch=b64 -S finit_module -S init_module -S delete_module -F auid!=-1 -k T1215_Kernel_Modules_and_Extensions_4
-a always,exit -F arch=b32 -S finit_module -S init_module -S delete_module -F auid!=-1 -k T1215_Kernel_Modules_and_Extensions_5
-w /etc/modprobe.conf -p wa -k T1215_Kernel_Modules_and_Extensions_6

## Time Related Events
-a exit,always -F arch=b32 -S adjtimex -S settimeofday -S clock_settime -k T1099_Timestomp_1
-a exit,always -F arch=b64 -S adjtimex -S settimeofday -S clock_settime -k T1099_Timestomp_2
-a always,exit -F arch=b32 -S clock_settime -k T1099_Timestomp_3
-a always,exit -F arch=b64 -S clock_settime -k T1099_Timestomp_4
-w /etc/localtime -p wa -k T1099_Timestomp_5

## Stunnel 
-w /usr/sbin/stunnel -p x -k T1079_Multilayer_Encryption_1

## Cron configuration & scheduled jobs related events
-w /etc/cron.allow -p wa -k T1168_Local_Job_Scheduling_1
-w /etc/cron.deny -p wa -k T1168_Local_Job_Scheduling_2
-w /etc/cron.d/ -p wa -k T1168_Local_Job_Scheduling_3
-w /etc/cron.daily/ -p wa -k T1168_Local_Job_Scheduling_4
-w /etc/cron.hourly/ -p wa -k T1168_Local_Job_Scheduling_5
-w /etc/cron.monthly/ -p wa -k T1168_Local_Job_Scheduling_6
-w /etc/cron.weekly/ -p wa -k T1168_Local_Job_Scheduling_7
-w /etc/crontab -p wa -k T1168_Local_Job_Scheduling_8
-w /var/spool/cron/crontabs/ -k T1168_Local_Job_Scheduling_9
-w /etc/inittab -p wa -k T1168_Local_Job_Scheduling_10
-w /etc/init.d/ -p wa -k T1168_Local_Job_Scheduling_11
-w /etc/init/ -p wa -k T1168_Local_Job_Scheduling_12
-w /etc/at.allow -p wa -k T1168_Local_Job_Scheduling_13
-w /etc/at.deny -p wa -k T1168_Local_Job_Scheduling_14
-w /var/spool/at/ -p wa -k T1168_Local_Job_Scheduling_15
-w /etc/anacrontab -p wa -k T1168_Local_Job_Scheduling_16

## Account Related Events
-w /etc/sudoers -p wa -k T1078_Valid_Accounts_1
-w /usr/bin/passwd -p x -k T1078_Valid_Accounts_2
-w /usr/sbin/groupadd -p x -k T1078_Valid_Accounts_3
-w /usr/sbin/groupmod -p x -k T1078_Valid_Accounts_4
-w /usr/sbin/addgroup -p x -k T1078_Valid_Accounts_5
-w /usr/sbin/useradd -p x -k T1078_Valid_Accounts_6
-w /usr/sbin/usermod -p x -k T1078_Valid_Accounts_7
-w /usr/sbin/adduser -p x -k T1078_Valid_Accounts_8

## Privleged Command Execution Related Events
#Commenting out the following line in order to reduce noise in the logs
#Done by Paul Farag on 5-8-2019
##UNCOMMENT THE TWO LINES BELOW IF THE SYSTEM NEEDS COMMAND HISTORY TO BE CAPTURED WITHIN AUDITD LOGS###
#-a exit,always -F arch=b64 -F euid=0 -S execve -k T1078_Valid_Accounts_9
#-a exit,always -F arch=b32 -F euid=0 -S execve -k T1078_Valid_Accounts_10
-a always,exit -F path=/usr/sbin/userdel -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_11
#Changed the numbers on these two lines
#-a exit,never -F path=/bin/ping -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_12
#-a exit,never -F path=/usr/bin/ping -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_12
###End Change
-a always,exit -F path=/bin/umount -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_13
-a always,exit -F path=/bin/mount -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_14
-a always,exit -F path=/bin/su -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_15
-a always,exit -F path=/bin/chgrp -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_16
#Changed the following two lines
#-a exit,never -F path=/bin/ping6 -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_17
#-a exit,never -F path=/usr/bin/ping6 -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_17
###End Change
-a always,exit -F path=/sbin/pam_timestamp_check -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_18
-a always,exit -F path=/sbin/unix_chkpwd -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_19
-a always,exit -F path=/sbin/pwck -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_20
-a always,exit -F path=/usr/sbin/suexec -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_21
-a always,exit -F path=/usr/sbin/usermod -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_22
-a always,exit -F path=/usr/sbin/newusers -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_23
-a always,exit -F path=/usr/sbin/groupdel -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_24
-a always,exit -F path=/usr/sbin/semanage -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_25
-a always,exit -F path=/usr/sbin/usernetctl -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_26
-a always,exit -F path=/usr/sbin/ccreds_validate -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_27
-a always,exit -F path=/usr/sbin/userhelper -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_28
##-a always,exit -F path=/usr/libexec/openssh/ssh-keysign -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_29
-a always,exit -F path=/usr/bin/Xorg -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_30
-a always,exit -F path=/usr/bin/rlogin -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_31
-a always,exit -F path=/usr/bin/sudoedit -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_32
-a always,exit -F path=/usr/bin/at -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_33
-a always,exit -F path=/usr/bin/rsh -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_34
-a always,exit -F path=/usr/bin/gpasswd -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_35
-a always,exit -F path=/usr/bin/kgrantpty -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_36
-a always,exit -F path=/usr/bin/crontab -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_37
-a always,exit -F path=/usr/bin/sudo -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_38
-a always,exit -F path=/usr/bin/staprun -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_39
-a always,exit -F path=/usr/bin/rcp -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_40
-a always,exit -F path=/usr/bin/passwd -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_41
-a always,exit -F path=/usr/bin/chsh -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_42
-a always,exit -F path=/usr/bin/chfn -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_43
-a always,exit -F path=/usr/bin/chage -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_44
-a always,exit -F path=/usr/bin/setfacl -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_45
-a always,exit -F path=/usr/bin/chacl -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_46
-a always,exit -F path=/usr/bin/chcon -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_47
-a always,exit -F path=/usr/bin/newgrp -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_48
-a always,exit -F path=/usr/bin/newrole -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_49
-a always,exit -F path=/usr/bin/kpac_dhcp_helper -F perm=x -F auid>=500 -F auid!=********** -k T1078_Valid_Accounts_50

## Media Export Related Events
-a always,exit -F arch=b32 -S mount -F auid>=500 -F auid!=********** -k T1052_Exfiltration_Over_Physical_Medium_1
-a always,exit -F arch=b64 -S mount -F auid>=500 -F auid!=********** -k T1052_Exfiltration_Over_Physical_Medium_2

## Session Related Events 
-w /var/run/utmp -p wa -k T1108_Redundant_Access_1
-w /var/log/wtmp -p wa -k T1108_Redundant_Access_2
-w /var/log/btmp -p wa -k T1108_Redundant_Access_3

## Login Related Events
-w /var/log/faillog -p wa -k T1021_Remote_Services_1
-w /var/log/lastlog -p wa -k T1021_Remote_Services_2
-w /var/log/tallylog -p wa -k T1021_Remote_Services_3

## Pam Related Events
-w /etc/pam.d/ -p wa -k T1071_Standard_Application_Layer_Protocol_1
-w /etc/security/limits.conf -p wa  -k T1071_Standard_Application_Layer_Protocol_2
-w /etc/security/pam_env.conf -p wa -k T1071_Standard_Application_Layer_Protocol_3
-w /etc/security/namespace.conf -p wa -k T1071_Standard_Application_Layer_Protocol_4
-w /etc/security/namespace.init -p wa -k T1071_Standard_Application_Layer_Protocol_5
-w /etc/pam.d/common-password -p wa -k T1201_Password_Policy_Discovery_1

## SSH Related Events
-w /etc/ssh/sshd_config -k T1021_Remote_Services_4

#Log 32 bit processes (a0=3 means only outbound sys_connect calls)
-a exit,always -F arch=b32 -S socketcall -F a0=3 -k T1043_Commonly_Used_Port_2

## Priv Escalation Related Events
-w /bin/su -p x -k T1169_Sudo_1
-w /usr/bin/sudo -p x -k T1169_Sudo_2
-w /etc/sudoers -p rw -k T1169_Sudo_3
-a always,exit -F dir=/home -F uid=0 -F auid>=1000 -F auid!=********** -C auid!=obj_uid -k T1169_Sudo_4
-a always,exit -F arch=b32 -S chmod -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_1
-a always,exit -F arch=b32 -S chown -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_2
-a always,exit -F arch=b32 -S fchmod -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_3
-a always,exit -F arch=b32 -S fchmodat -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_4
-a always,exit -F arch=b32 -S fchown -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_5
-a always,exit -F arch=b32 -S fchownat -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_6
-a always,exit -F arch=b32 -S fremovexattr -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_7
-a always,exit -F arch=b32 -S fsetxattr -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_8
-a always,exit -F arch=b32 -S lchown -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_9
-a always,exit -F arch=b32 -S lremovexattr -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_10
-a always,exit -F arch=b32 -S lsetxattr -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_11
-a always,exit -F arch=b32 -S removexattr -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_12
-a always,exit -F arch=b32 -S setxattr -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_13
-a always,exit -F arch=b64 -S chmod  -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_14
-a always,exit -F arch=b64 -S chown -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_15
-a always,exit -F arch=b64 -S fchmod -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_16
-a always,exit -F arch=b64 -S fchmodat -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_17
-a always,exit -F arch=b64 -S fchown -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_18
-a always,exit -F arch=b64 -S fchownat -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_19
-a always,exit -F arch=b64 -S fremovexattr -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_20
-a always,exit -F arch=b64 -S fsetxattr -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_21
-a always,exit -F arch=b64 -S lchown -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_22
-a always,exit -F arch=b64 -S lremovexattr -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_23
-a always,exit -F arch=b64 -S lsetxattr -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_24
-a always,exit -F arch=b64 -S removexattr -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_25
-a always,exit -F arch=b64 -S setxattr -F auid>=500 -F auid!=********** -k T1166_Seuid_and_Setgid_26
#-a always,exit -F arch=b64 -C auid!=uid -S execve -k T1166_Seuid_and_Setgid_27
#Commenting the following out to reduce noise in the log
# Done by Paul Farag on 5-8-2019
#-a always,exit -F arch=b32 -C auid!=uid -S execve -F a0!=5 -F a2!=10 -k T1166_Seuid_and_Setgid_28
#-a always,exit -F arch=b64 -S setuid -S setgid -S setreuid -S setregid -F a0!=5 -F a2!=10 -k T1166_Seuid_and_Setgid_29
-a always,exit -F arch=b32 -S setuid -S setgid -S setreuid -S setregid -k T1166_Seuid_and_Setgid_30
-a always,exit -F arch=b64 -S setuid -S setgid -S setreuid -S setregid -F exit=EPERM -k T1166_Seuid_and_Setgid_31
-a always,exit -F arch=b32 -S setuid -S setgid -S setreuid -S setregid -F exit=EPERM -k T1166_Seuid_and_Setgid_32
 -w /usr/bin/ -p wa -k T1068_Exploitation_for_Privilege_Escalation_1

## Recon Related Events
-w /etc/group -p wa -k T1087_Account_Discovery_1
-w /etc/passwd -p wa -k T1087_Account_Discovery_2
-w /etc/gshadow -k T1087_Account_Discovery_3
-w /etc/shadow -k T1087_Account_Discovery_4
-w /etc/security/opasswd -k T1087_Account_Discovery_5
-w /usr/sbin/nologin -k T1087_Account_Discovery_6
-w /sbin/nologin -k T1087_Account_Discovery_7
-w /usr/bin/whoami -p x -k T1033_System_Owner_User_Discovery_1
-w /etc/hostname -p r -k T1082_System_Information_Discovery_1
-w /sbin/iptables -p x -k T1082_System_Information_Discovery_2
-w /sbin/ifconfig -p x -k T1082_System_Information_Discovery_3
-w /etc/login.defs -p wa -k T1082_System_Information_Discovery_4
-w /etc/resolv.conf -k T1016_System_Network_Configuration_Discovery_1
-w /etc/hosts.allow -k T1016_System_Network_Configuration_Discovery_2
-w /etc/hosts.deny -k T1016_System_Network_Configuration_Discovery_3
-w /etc/securetty -p wa -k T1082_System_Information_Discovery_5
-w /var/log/faillog -p wa -k T1082_System_Information_Discovery_6
-w /var/log/lastlog -p wa -k T1082_System_Information_Discovery_7
-w /var/log/tallylog -p wa -k T1082_System_Information_Discovery_8
-w /usr/sbin/tcpdump -p x -k T1049_System_Network_Connections_discovery_9
-w /usr/sbin/traceroute -p x -k T1049_System_Network_Connections_discovery_10
-w /usr/bin/wireshark -p x -k T1049_System_Network_Connections_discovery_11
-w /usr/bin/rawshark -p x -k T1049_System_Network_Connections_discovery_12
-w /usr/bin/grep -p x -k T1081_Credentials_In_Files_1
-w /usr/bin/egrep -p x -k T1081_Credentials_In_Files_2
-w /usr/bin/ps -p x -k T1057_Process_Discovery_1

## Data Copy(Local)
-w /usr/bin/cp -p x -k T1005_Data_from_Local_System_1
-w /usr/bin/dd -p x -k T1005_Data_from_Local_System_2

## Remote Access Related Events
-w /usr/bin/wget -p x -k T1219_Remote_Access_Tools_1
-w /usr/bin/curl -p x -k T1219_Remote_Access_Tools_2
-w /usr/bin/base64 -p x -k T1219_Remote_Access_Tools_3
-w /bin/nc -p x -k T1219_Remote_Access_Tools_4
-w /bin/netcat -p x -k T1219_Remote_Access_Tools_5
-w /usr/bin/ncat -p x -k T1219_Remote_Access_Tools_6
-w /usr/bin/ssh -p x -k T1219_Remote_Access_Tools_7
-w /usr/bin/socat -p x -k T1219_Remote_Access_Tools_8
-w /usr/bin/rdesktop -p x -k T1219_Remote_Access_Tools_9

##Third Party Software 
# RPM (Redhat/CentOS)
-w /usr/bin/rpm -p x -k T1072_third_party_software_1
-w /usr/bin/yum -p x -k T1072_third_party_software_2

# YAST/Zypper/RPM (SuSE)
-w /sbin/yast -p x -k T1072_third_party_software_3
-w /sbin/yast2 -p x -k T1072_third_party_software_4
-w /bin/rpm -p x -k T1072_third_party_software_5
-w /usr/bin/zypper -k T1072_third_party_software_6

# DPKG / APT-GET (Debian/Ubuntu)
-w /usr/bin/dpkg -p x -k T1072_third_party_software_7
-w /usr/bin/apt-add-repository -p x -k T1072_third_party_software_8
-w /usr/bin/apt-get -p x -k T1072_third_party_software_9
-w /usr/bin/aptitude -p x -k T1072_third_party_software_10

## Code injection Related Events
-a always,exit -F arch=b32 -S ptrace -k T1055_Process_Injection_1
-a always,exit -F arch=b64 -S ptrace -k T1055_Process_Injection_2
-a always,exit -F arch=b32 -S ptrace -F a0=0x4 -k T1055_Process_Injection_3
-a always,exit -F arch=b64 -S ptrace -F a0=0x4 -k T1055_Process_Injection_4
-a always,exit -F arch=b32 -S ptrace -F a0=0x5 -k T1055_Process_Injection_5
-a always,exit -F arch=b64 -S ptrace -F a0=0x5 -k T1055_Process_Injection_6
-a always,exit -F arch=b32 -S ptrace -F a0=0x6 -k T1055_Process_Injection_7
-a always,exit -F arch=b64 -S ptrace -F a0=0x6 -k T1055_Process_Injection_8

## Shell configuration Persistence Related Events
-w /etc/profile.d/ -k T1156_bash_profile_and_bashrc_1
-w /etc/profile -k T1156_bash_profile_and_bashrc_2
-w /etc/shells -k T1156_bash_profile_and_bashrc_3
-w /etc/bashrc -k T1156_bash_profile_and_bashrc_4
-w /etc/csh.cshrc -k T1156_bash_profile_and_bashrc_5
-w /etc/csh.login -k T1156_bash_profile_and_bashrc_6

#Remote File Copy
-w /usr/bin/ftp -p x -k T1105_remote_file_copy_1

## File Deletion by User Related Events
-a always,exit -F arch=b32 -S rmdir -S unlink -S unlinkat -S rename -S renameat -F auid>=500 -F auid!=********** -k T1107_File_Deletion_1
-a always,exit -F arch=b64 -S rmdir -S unlink -S unlinkat -S rename -S renameat -F auid>=500 -F auid!=********** -k T1107_File_Deletion_2
-a always,exit -F arch=b32 -S rmdir -S unlink -S unlinkat -S rename -S renameat -F auid=0 -k T1070_Indicator_Removal_on_Host_1
-a always,exit -F arch=b64 -S rmdir -S unlink -S unlinkat -S rename -S renameat -F auid=0 -k T1070_Indicator_Removal_on_Host_2


#########append related to MPS rules from mps17 version###########
#OS MPS specific rules
-w /etc/ld.so.conf -p wa -k CFG_ld.so.conf
-a exit,always -F arch=b64  -S sethostname
-w /etc/issue -p wa -k CFG_issue
-w /etc/issue.net -p wa -k CFG_issue.net
-w /etc/fstab -p wa -k CFG_fstab
-w /etc/logrotate.d/ -p wa -k CFG_logrotate.d

-e 2
