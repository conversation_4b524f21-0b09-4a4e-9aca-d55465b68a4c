- name: Add tac user and add it to sudo
  user: 
    name: tac
    uid: 950
    state: present
    createhome: yes
    shell: /bin/bash
    group: sudo
    append: yes

- name: Modify password - tac
  shell: |
       set -o pipefail
       USER=tac
       PASSWORD='{{tac_password}}'
       if [[ "$PASSWORD" == "NOT_SET" ]]; then 
          password_set=$(cat /etc/shadow | grep "^${USER}:" | wc -l )
          if [[ "$password_set" == "1" ]]; then 
              echo "Updating password - removing"
              passwd --delete tac
          fi 
       else         
          password_exists=$(cat /etc/shadow | grep "^${USER}:" | grep -c ":${PASSWORD}:" )
          if [[ "$password_exists" -ne "0" ]]; then
                echo "Password already set properly"
          else
                echo "Updating password"
                usermod -p "${PASSWORD}" ${USER}
          fi
       fi
  register: modify_password
  changed_when: modify_password.stdout.find('Updating password') != -1

#- name: Install libpam-cisco-ct
#  apt:
#    state: present
#    force_apt_get: yes
#    pkg:
#      - libpam-cisco-ct


- name: Copy pam_cisco_ct.ini
  copy:
    src: "files/pam_cisco_ct.ini"
    dest: "/etc/security/pam_cisco_ct.ini"
    mode: 0644

- name: Copy tac-users
  copy:
    src: "files/tac-users"
    dest: "/etc/sudoers.d/tac-users"
    mode: 0440

- name: Update pamd rule /etc/pam.d/sshd
  lineinfile:
    path: /etc/pam.d/sshd
    regexp: '^.*pam_cisco_ct.so'
    insertafter: '^.*include.*common-account'
    line: 'auth [user_unknown=ignore default=done] pam_cisco_ct.so'

- name: Update pamd rule /etc/pam.d/su
  lineinfile:
    path: /etc/pam.d/su
    regexp: '^.*pam_cisco_ct.so'
    insertafter: '^.*include.*common-account'
    line: 'auth [user_unknown=ignore default=done] pam_cisco_ct.so'

- name: Update pamd rule /etc/pam.d/login
  lineinfile:
    path: /etc/pam.d/login
    regexp: '^.*pam_cisco_ct.so'
    insertafter: '^.*include.*common-account'
    line: 'auth [user_unknown=ignore default=done] pam_cisco_ct.so'

- name: Update pamd rule /etc/ssh/sshd_config
  lineinfile:
    path: /etc/ssh/sshd_config
    regexp: '^ChallengeResponseAuthentication'
    line: 'ChallengeResponseAuthentication yes'
  notify:
      - "reload_daemon"
      - "restart_sshd" 
