- name: Copy agent-manager-client systemd template file
  become: yes
  template:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    mode: 0644
  with_items:
    - {
        src: "agent-manager-core@.service",
        dest: "/etc/systemd/system/agent-manager-core@.service",
      }

- name: Enable agent-manager-core service manually
  become: yes
  command: systemctl enable agent-manager-core@agent-manager

- name: Start agent-manager-core service manually
  become: yes
  command: systemctl start agent-manager-core@agent-manager
 
#- name: Enable agent-manager-core agent-manager service
#  become: yes
#  register: agent_manager_core_service
#  systemd:
#    name: <EMAIL>
#    enabled: yes
#    state: started

