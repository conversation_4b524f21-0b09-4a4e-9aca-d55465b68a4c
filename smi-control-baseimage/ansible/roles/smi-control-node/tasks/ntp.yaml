- name: Ensure ntpdate is present on host
  apt:
    state: present
    pkg:
        - ntpdate

- name: Remove "ntp" package
  apt:
    state: absent
    pkg:
      - ntp

#- name: Disable systemd-timesyncd
#  service:
#    name: systemd-timesyncd.service
#    enabled: no
#    state: stopped

- name: Install chrony
  apt:
    state: present
    force_apt_get: yes
    pkg:
      - chrony

- name: enable chrony ntp
  systemd:
    name: chrony
    state: started
    enabled: yes

- name: Update the chrony keys
  template:
      src: chrony.keys
      dest: /etc/chrony/chrony.keys
      mode: "0640"
      force: yes

- name: Update the chrony conf
  template:
      src: chrony.conf
      dest: /etc/chrony/chrony.conf
      mode: "0644"
      force: yes
  notify:
    - "restart_chrony"
#    - "force_time_sync"
#    - "verify_chrony_status"
#    - "check_system_time"

