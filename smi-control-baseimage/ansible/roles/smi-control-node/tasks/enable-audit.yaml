- name: Creates /var/log/audit directory
  file:
    path: /var/log/audit
    state: directory
    owner: root
    group: root
    mode: 0755

- name: Install auditd
  apt:
    state: present
    force_apt_get: yes
    pkg:
      - auditd
  retries: 1
  delay: 10
  register: result
  until: result is not failed

- name: Ensure audit logs are rotated
  lineinfile:
    state: present
    dest: "/etc/audit/auditd.conf"
    regexp: "^#?max_log_file_action ="
    line: "max_log_file_action = rotate"
  notify: restart_auditd

- name: Set audit logs max file size
  lineinfile:
    state: present
    dest: "/etc/audit/auditd.conf"
    regexp: "^#?max_log_file ="
    line: "max_log_file = 100"
  notify: restart_auditd

- name: Set num of audit logs files to keep
  lineinfile:
    state: present
    dest: "/etc/audit/auditd.conf"
    regexp: "^#?num_logs ="
    line: "num_logs = 10"
  notify: restart_auditd

- name: Ensure auditd sysctl process running
  systemd:
    state: started
    name: auditd
    enabled: yes

- name: Configured audit rules
  template:
    src: audit.rules
    dest: /etc/audit/rules.d/audit.rules
    force: yes
  register: change_audit