#! /bin/bash
set -e
set -o pipefail

CHRONY_VERSION="3.2-4ubuntu*"
NTPDATE_VERSION="1:4.2.8p10+dfsg-5ubuntu*"
ART_USER="smi-readonly.gen"
ART_PASS="AKCp5ekHjovek3NLqcMwePLcpsYUmuYgApYWGP54sRaKM1qQV8omemPFaCFWxFxaMV8eF2FqK"

rm -rf /etc/apt.sources.d/*
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | apt-key add -
add-apt-repository \
   "deb [arch=amd64] https://download.docker.com/linux/ubuntu \
   $(lsb_release -cs) \
   stable"
apt-get update
apt-get install -y --no-install-recommends --download-only \
                ntpdate=${NTPDATE_VERSION} \
                chrony=${CHRONY_VERSION}
curl -f -u ${ART_USER}:${ART_PASS} -o /var/cache/apt/archives/libpam-cisco-ct_0.3.0_amd64.deb \
    https://engci-maven-master.cisco.com/artifactory/smi-fuse-internal-group/com/cisco/pam-cisco-ct/libpam-cisco-ct_0.3.0_amd64.deb
ls -altr /var/cache/apt/archives
mkdir /var/tmp/download_deb
chown -Rv _apt:root /var/tmp/download_deb
mv /var/cache/apt/archives/libpam-cisco-ct_0.3.0_amd64.deb /var/tmp/download_deb/
chown -Rv _apt:root /var/tmp/download_deb/libpam-cisco-ct_0.3.0_amd64.deb
chmod -Rv 700 /var/tmp/download_deb/
update-initramfs -u

if [ $(service docker status | grep active | wc -l) -ne 1 ]; then
  service docker start
fi
sleep 5
docker load < /docker.tar
docker load < /registry.tar

sed -i 's|PasswordAuthentication no|PasswordAuthentication yes|g' /etc/ssh/sshd_config
ln -sf /run/systemd/resolve/resolv.conf /etc/resolv.conf

function cleanup() {
  echo "Killing tail: ${TAIL_PID}"
  kill -9 $TAIL_PID
}
pushd /ansible

tail -F /var/tmp/build.log &
TAIL_PID=$!
trap cleanup EXIT

python3 /opt/ansible-playbook -i inventory.yaml site.yaml
