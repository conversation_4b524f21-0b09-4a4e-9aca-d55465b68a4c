#!/bin/bash
VULCAN='dockerhub.cisco.com/smi-fuse-docker-internal/smi-libraries/vulcan/master/vulcan:1.0.2-0265-39b9891'
COMMAND="./build-base-image.sh"
if [[ -n "$1" ]]; then
  COMMAND="$1"
fi
docker pull ${VU<PERSON><PERSON>}

docker run --rm \
  -e=https_proxy=${https_proxy} \
  -e=http_proxy=${https_proxy} \
  -e=no_proxy=${no_proxy} \
  --privileged \
  -v /dev:/dev \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v $(pwd):/smi-base-image \
  -w /smi-base-image \
  ${VULCAN} ${COMMAND}
