.env
.coverage
.vulcan
/base-vm
*.iso
*.img
*.raw
*.vmdk
*.qcow2
downloads
*.tar.gz
*.tar.xz
*.tar
htmlcov
/build
**offline.tgz
**offline.tgz.txt
**.retry
tools
vars/variables-dev.yml
charts
node_modules
.log
example-config.yaml
__pycache__
*.pyc
images.yaml
dive.log
.DS_Store
.vagrant
*~
\#*\#
.\#*
*.egg-info
.tox
.mypy_cache
dist
.eggs
ansible-hardening-test/roles/
files/offline_release/*.tar
artifacts
base-image
ovf
