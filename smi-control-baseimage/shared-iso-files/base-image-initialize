#!/bin/busybox sh
set -e

#Outputs to console and serial port for debugging purposes
output_message() {
   local message=$1
   local stdout=$2
   if [[ -z "$stdout" ]]; then
     stdout="true"
   fi
   local time=$(awk '{print $1}' /proc/uptime)
   echo "[smi-init - $time] $message" > /dev/ttyS0 2> /dev/null || true
   if [[ "$stdout" == "true" ]]; then
     echo "[smi-init - $time] $message"
   fi
}

#Put partition IDs and labels into variables to help keep things straight and make refactor possible
id_part_a="1"
id_part_b="2"
id_part_logs="3"
logs_label="smi-logs"
id_part_tmp="4"
tmp_label="smi-tmp"
id_part_state="5"
state_label="smi-state"
id_part_bios="14"
bios_label="bios"
id_part_efi="15"
efi_label="uefi"
id_part_cloud_config="12"
smi_cloud_config_label="smi-cloud-config"
id_part_reserved="13"
smi_reserved_label="smi-reserved"

smi_reserved_mib=$((2048))
smi_reserved_bytes=$(( $smi_reserved_mib * 1024 * 1024))

smi_cloud_config_mib=$((32))
smi_cloud_config_bytes=$(( $smi_cloud_config_mib * 1024 * 1024))

#Technically a smaller size would sort of function - but 40GB seems like a reasonable minimum.
#Not sure if that is workable for production though
min_disk_size_gb=40

partition_drive() {
  local device=$1
  output_message "Partitioning hard drive device"

  local total_disk_size_sectors=$(sgdisk -p ${device}| grep "Disk ${device}" | sed -r 's/.*: (.*) sectors.*/\1/g')
  output_message "Total Disk Sectors: $total_disk_size_sectors"
  local total_disk_bytes=$(( $total_disk_size_sectors * 512 ))
  
  local bios_mib=4
  local efi_mib=128
  
  local boot_total_mib=$(($bios_mib + $efi_mib + $smi_reserved_mib + $smi_cloud_config_mib ))
  local boot_total_bytes=$(($boot_total_mib * 1024 * 1024))
  
  local partition_gib=10
  local partition_bytes=$(($partition_gib * 1024 * 1024 * 1024))
  local partition_total_bytes=$((2 * $partition_bytes))
  
  local total_overhead_bytes=$(( $partition_total_bytes + $boot_total_bytes + $smi_reserved_bytes + $smi_cloud_config_bytes ))
  local total_available_bytes=$(( $total_disk_bytes - $total_overhead_bytes ))
  local total_available_mib=$(( $total_available_bytes / 1024 / 1024 ))
  
  output_message "Total available MB after partition and bios overhead: ${total_available_mib}"
  
  local logs_percentage="0.1"
  local var_tmp_percentage="0.1"
  output_message "Total available bytes: ${total_available_bytes}"
  local stateful_percentage=$(echo "scale=10; 1.0-$logs_percentage-$var_tmp_percentage" | bc -l)
  
  local logs_mib=$(echo "scale=0; ($total_available_bytes * $logs_percentage)/1024/1024 " |bc -l)
  local var_tmp_mib=$(echo "scale=0; ($total_available_bytes * $var_tmp_percentage)/1024/1024" |bc -l)
  local stateful_mib=$(echo "scale=0; (($total_available_bytes * $stateful_percentage)/1024/1024) - 2" |bc -l)
  output_message "/var/logs (MiB): ${logs_mib} /var/tmp (MiB): ${var_tmp_mib}"
  output_message "/mnt/stateful_partition (MiB): ${stateful_mib} "
  
  output_message "Addinng partitions"
  sgdisk ${device} \
         -n ${id_part_b}:0:+${partition_gib}GiB -c "${id_part_b}:smi-root-b" \
         -n ${id_part_tmp}:0:+${var_tmp_mib}MiB -c "${id_part_tmp}:${tmp_label}" \
         -n ${id_part_logs}:0:+${logs_mib}MiB -c "${id_part_logs}:${logs_label}" \
         -n ${id_part_state}:0:+${stateful_mib}MiB -c "${id_part_state}:${state_label}" \

  partprobe
}
get_partition() {
  local device=$1
  local partition=$2
  
  #NOTE: grep is used to match a number at the end of device because
  #busybox (ash shell) does not support regex comparisions
  if echo "$device" | grep '[0-9]\+$' > /dev/null; then
    echo "${device}p${partition}"
  else
    echo "${device}${partition}"
  fi
}
format_partitions() {
  local device=$1
  mkfs.ext4 $(get_partition ${device} ${id_part_logs})  -F -L "${logs_label}"
  TEMP_LOGS=/mnt/logs
  mkdir -p ${TEMP_LOGS}
  mount $(get_partition ${device} ${id_part_logs}) ${TEMP_LOGS}
  mkdir -p ${TEMP_LOGS}/audit
  umount ${TEMP_LOGS}
  mkfs.ext4 $(get_partition ${device} ${id_part_tmp})  -F -L "${tmp_label}"
  mkfs.ext4 $(get_partition ${device} ${id_part_state})  -F -L "${state_label}"
  #This is to ensure an old 'b' partition doesn't get mounted by fstab or grub
  mkfs.ext4 $(get_partition ${device} ${id_part_b})  -F -L "smi-backup"

}


initialize_extra_disk() {
  disk_device="/dev/$1"
  disk_label=$2
  partition_id=1
  output_message "Initializing device: ${disk_device} partition: ${partition_id} label: ${disk_label}"
  sgdisk -Z ${disk_device} -n ${partition_id} -c "${partition_id}:${disk_label}"
  partprobe ${disk_device}
  
  device_formatted=$(blkid | grep " LABEL=\"${disk_label}\"" | head -1 | cut -d':' -f1 || true)

  if [[ -z "${device_formatted}" ]]; then
    output_message "Device not already formatted - reformatting"
    mkfs.ext4 ${disk_device}${partition_id} -F -L "${disk_label}"
  else
    output_message "Device previously formatted as ${disk_label}.  Leaving..."
  fi
}

disk_with_serial() {
  serial_starts_with=$1
  echo $(lsblk -o NAME,SERIAL | grep "$serial_starts_with" | cut -f1 -d' ' | xargs || true)
}

get_hard_drive_device() {

  set +e
  output_message "$(env)" "false"
  output_message "root: $ROOT" "false"
  if [ -n "$ROOT" ]; then
    label=$(echo $ROOT | sed 's/.*LABEL=//g')
    output_message "label: ${label}" "false"
    echo $(blkid | grep " LABEL=\"${label}\"" | head -1 | cut -d':' -f1 | sed 's|[0-9]*$||g' | sed -r 's|([0-9])p$|\1|g' || true)
  fi
  set -e
  
}

add_disk_to_fstab() {
  
  DEVICE=$1
  MOUNT_DIR=$2
  LABEL=$3
  ROOT_MOUNT=$4
  DEFAULTS_OVERRIDE=$5

  if grep "$DEVICE\s*" ${ROOT_MOUNT}/etc/fstab; then
      output_message "Entry ${DEVICE} ${MOUNT_DIR} in ${ROOT_MOUNT}/etc/fstab exists"
      return
  fi
  if grep "LABEL=${LABEL}\s*" ${ROOT_MOUNT}/etc/fstab; then
      output_message "Entry ${LABEL} ${MOUNT_DIR} in ${ROOT_MOUNT}/etc/fstab exists"
      return
  fi
  if [ ! -z $DEFAULTS_OVERRIDE ]; then
      echo "LABEL=$LABEL $MOUNT_DIR ext4 $DEFAULTS_OVERRIDE 0 0" >> ${ROOT_MOUNT}/etc/fstab
  else
      echo "LABEL=$LABEL $MOUNT_DIR ext4 defaults 0 0" >> ${ROOT_MOUNT}/etc/fstab
  fi

  
}

add_bind_mount_to_fstab() {
  
  ROOT_DIR=$1
  BIND_DIR=$2
  ROOT_MOUNT=$3
  if [[ -z "${ROOT_MOUNT}" ]]; then
    ROOT_MOUNT="/"
  fi
  if grep "$BIND_DIR" ${ROOT_MOUNT}/etc/fstab; then
      output_message "Bind Entry ${ROOT_DIR} ${BIND_DIR} in /etc/fstab exists"
      mkdir -p ${ROOT_MOUNT}$ROOT_DIR
      mkdir -p ${ROOT_MOUNT}$BIND_DIR
  else
      echo "$ROOT_DIR $BIND_DIR none defaults,bind 0 0" >> ${ROOT_MOUNT}/etc/fstab
      output_message "Added: ${BIND_DIR} to ${ROOT_MOUNT}/etc/fstab"
      if [[ ! -d "${ROOT_DIR}" ]]; then
        output_message "${ROOT_DIR} does not exist"
        if [[ -d "${ROOT_MOUNT}${BIND_DIR}" ]]; then
          output_message "Initializing bind dir (${ROOT_DIR}) from existing (${ROOT_MOUNT}${BIND_DIR})..."
          mkdir -p $(dirname ${ROOT_DIR}) #Create the parent directory as it probably doesn't exist
          mv "${ROOT_MOUNT}${BIND_DIR}" "${ROOT_DIR}"
          output_message "Done initializing"
        else
          output_message "${ROOT_MOUNT}${BIND_DIR} does not exist"
        fi
      fi

  fi
}

cleanup() {

  umount ${TEMP_MOUNT} || true
}

grow_existing_partition() {
  DEVICE=$1
  PARTITION_ID=$2

  output_message "Checking if growing partition is needed on ${DEVICE}${PARTITION_ID}"
  if growpart --dry-run ${DEVICE} ${PARTITION_ID}; then
     
     output_message "Partition: ${DEVICE}${PARTITION_ID} can be grown"
     growpart ${DEVICE} ${PARTITION_ID}
     partprobe
     output_message "Partition: ${DEVICE}${PARTITION_ID} has been grown to fill all space"
     e2fsck -y -f ${DEVICE}${PARTITION_ID} || true
     output_message "Filesystem check (e2fsck) done"

     resize2fs ${DEVICE}${PARTITION_ID}
     output_message "Underlying file system resized on ${DEVICE}${PARTITION_ID}"
  else
     output_message "Partition: ${DEVICE}${PARTITION_ID} does not need to be grown"
  fi
}
print_debug_info() {

DEBUG_INFO=$(cat <<-EOM

Network Info (ip addr): 
------------------------
$(ip addr)

Disks - Mounted (df):
------------------------
$(df)

Disks - All (lsblk):
------------------------
$(lsblk)

Disk IDs (blkid):
------------------------
$(blkid)

Kernel Parameters (cat /proc/cmdline):
------------------------
$(cat /proc/cmdline)

System Information (dmidecode -t system):
------------------------
$(dmidecode -t system)

EOM
)

output_message "$DEBUG_INFO"

}
print_debug_info
hard_drive_device=$(get_hard_drive_device)
echo "hard drive device=${hard_drive_device}"
if [[ -z "${hard_drive_device}" ]]; then
  output_message "Could not find the root disk.  This is probably a coding error.  Continuing boot."
  exit 0
fi
output_message "Hard drive device: ${hard_drive_device}"

total_free_sectors=$(sgdisk -p ${hard_drive_device} | grep "Total free space" | sed -r 's/Total free space is ([0-9]*) .*/\1/g')
total_free_bytes=$(( total_free_sectors * 512 ))
minimum_size_needed=$(( 1 * 1024 * 1024 * 1024))

output_message "Free bytes: ${total_free_bytes}"
output_message "Free sectors: ${total_free_sectors}"
output_message "Minimum size needed to partition: ${minimum_size_needed}"

if [[ "${total_free_bytes}" -lt "${minimum_size_needed}" ]]; then
  output_message "Not enough free space to do anything with partitioning.  Skipping"
else
  output_message "Finishing partitioning of disk"
  a_setup=$(lsblk -o PARTLABEL --fs | grep "smi-root-a$" )
  if [[ -z "${a_setup}" ]]; then
     output_message "This partitioning scheme does not include smi-root-a and appears to not be understood (upgrade?).  Exiting"
     exit 0
  fi

  reserved_setup=$(lsblk -o PARTLABEL | grep "${smi_reserved_label}$" || true)
  cloud_init_setup=$(lsblk -o PARTLABEL --fs | grep "${smi_cloud_config_label}$" || true)
  b_setup=$(lsblk -o PARTLABEL| grep "smi-root-b$" || true)
  tmp_setup=$(lsblk -o PARTLABEL | grep "smi-tmp$" || true)
  logs_setup=$(lsblk -o PARTLABEL | grep "smi-logs$" || true)
  state_setup=$(lsblk -o PARTLABEL | grep "smi-state$" || true)

  #SMI reserved isn't *always* created.  Created if it doesn't exist
  if [ -z "${reserved_setup}" ]; then
    output_message "Creationg SMI Reserved Partition: smi-reserved"
    sgdisk ${hard_drive_device} -n ${id_part_reserved}:0:+${smi_reserved_mib}MiB -c "${id_part_reserved}:${smi_reserved_label}"
    mkfs.ext4 $(get_partition ${hard_drive_device} ${id_part_reserved})  -F -L "${smi_reserved_label}"

  else
    output_message "smi-reserved already created"
  fi
  if [ -z "${cloud_init_setup}" ]; then
    output_message "Creationg Cloud Config Partition: ${smi_cloud_config_label}"
    sgdisk ${hard_drive_device} -n ${id_part_cloud_config}:0:+${smi_cloud_config_mib}MiB -c "${id_part_cloud_config}:${smi_cloud_config_label}"
    mkfs.vfat -n cidata $(get_partition ${hard_drive_device} ${id_part_cloud_config})
  else
    output_message "${smi_cloud_config_label} partition already created"
  fi
  
  if [ -z "${b_setup}" ] && [ -z "${tmp_setup}" ] && [ -z "${logs_setup}" ] && [ -z "${state_setup}" ]; then
     output_message "Initializing partitions: smi-root-b, smi-tmp, smi-logs, smi-state"
     partition_drive ${hard_drive_device}
     format_partitions ${hard_drive_device}
  else
     output_message "Some partitions already appear to be setup.  Skipping...  b: ${b_setup} tmp: ${tmp_setup} logs: ${logs_setup} state: ${state_setup}"
  fi

fi
#Cleanup - unmount - Copy log to root disk for easier troubleshooting
trap cleanup EXIT

CISCO_SERIAL_PREFIX='6084fa900939'
SMI_DATA_SERIAL="${CISCO_SERIAL_PREFIX}0001"
SMI_HOME_SERIAL="${CISCO_SERIAL_PREFIX}0002"
SMI_ROOT_SERIAL="${CISCO_SERIAL_PREFIX}0003"

#data and home disks can be setup as separate disks - otherwise they will be bind mounted
data_setup=$(lsblk -o PARTLABEL | grep "smi-data$" || true)
home_setup=$(lsblk -o PARTLABEL | grep "smi-home$" || true)

#Check for VMWare disks to be initialized
if [[ -z "${data_setup}" ]]; then
  output_message "No separate data disk found - checking for data disk serial: ${SMI_DATA_SERIAL}"
  data_disk=$(disk_with_serial "$SMI_DATA_SERIAL")
  if [[ -n "${data_disk}" ]]; then
    output_message "Found data disk: ${data_disk} - initializing"
    initialize_extra_disk ${data_disk} "smi-data"
    output_message "Done initializing data disk"
    data_setup=$(lsblk -o PARTLABEL | grep "smi-data$" || true)
  fi
fi
if [[ -z "${home_setup}" ]]; then
  output_message "No separate home disk found - checking for home disk serial: ${SMI_HOME_SERIAL}"
  home_disk=$(disk_with_serial "$SMI_HOME_SERIAL")
  if [[ -n "${home_disk}" ]]; then
    output_message "Found home disk: ${home_disk} - initializing"
    initialize_extra_disk ${home_disk} "smi-home"
    output_message "Done initializing home disk"
    home_setup=$(lsblk -o PARTLABEL | grep "smi-home$" || true)
  fi
fi

output_message "Ensuring fstab is up to date"

TEMP_MOUNT=/mnt/temp
STATEFUL_PARTITION_DIR=/mnt/stateful_partition
mkdir -p "${TEMP_MOUNT}"
mkdir -p "${STATEFUL_PARTITION_DIR}"

root_disk=$(blkid | grep 'LABEL="cloudimg-rootfs"' | head -1 | cut -d':' -f1)
output_message "Root disk is: ${root_disk}"

mount $root_disk "${TEMP_MOUNT}"
mount $(get_partition ${hard_drive_device} ${id_part_state}) ${STATEFUL_PARTITION_DIR}
add_disk_to_fstab $(get_partition ${hard_drive_device} ${id_part_state}) /mnt/stateful_partition smi-state "${TEMP_MOUNT}"
add_disk_to_fstab $(get_partition ${hard_drive_device} ${id_part_logs}) /var/log smi-logs "${TEMP_MOUNT}"
add_disk_to_fstab $(get_partition ${hard_drive_device} ${id_part_tmp}) /tmp smi-tmp "${TEMP_MOUNT}" "defaults,noexec,nodev,nosuid"

add_bind_mount_to_fstab ${STATEFUL_PARTITION_DIR}/var/lib/docker /var/lib/docker "${TEMP_MOUNT}"
add_bind_mount_to_fstab ${STATEFUL_PARTITION_DIR}/var/lib/kubelet /var/lib/kubelet "${TEMP_MOUNT}"
add_bind_mount_to_fstab ${STATEFUL_PARTITION_DIR}/var/lib/systemd/coredump /var/lib/systemd/coredump "${TEMP_MOUNT}"
add_bind_mount_to_fstab ${STATEFUL_PARTITION_DIR}/var/lib/dockershim /var/lib/dockershim "${TEMP_MOUNT}"

#If data or home separate disks exist, mount them by label.  Otherwise bind mount
if [[ -n "${data_setup}" ]]; then
  add_disk_to_fstab ${data_setup}1 /data smi-data "${TEMP_MOUNT}"
else
  add_bind_mount_to_fstab ${STATEFUL_PARTITION_DIR}/data /data "${TEMP_MOUNT}"
fi
if [[ -n "${home_setup}" ]]; then
  add_disk_to_fstab ${home_setup}1 /home smi-home "${TEMP_MOUNT}"
else
  add_bind_mount_to_fstab ${STATEFUL_PARTITION_DIR}/home /home "${TEMP_MOUNT}"
fi

umount ${STATEFUL_PARTITION_DIR}

#State partition doesn't seem to be growing due to partprobe failure
#In the future this can be added
#grow_existing_partition ${hard_drive_device} ${id_part_state}

#NOTE: /data and /home will only be grown if first partition as otherwise something unknown is going on
if [[ -n "${data_setup}" ]]; then
  data_device=$(blkid -L smi-data | sed 's|1$||g')
  grow_existing_partition ${data_device} 1
fi
if [[ -n "${home_setup}" ]]; then
  home_device=$(blkid -L smi-home | sed 's|1$||g')
  grow_existing_partition ${home_device} 1
fi

output_message "====== /etc/fstab ========"
output_message "$(cat ${TEMP_MOUNT}/etc/fstab)"
