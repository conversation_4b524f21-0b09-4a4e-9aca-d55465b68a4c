#!/bin/sh
# The purpose of this script is to ensure *all* output gets written
# to log, stdout and serial port
set -e
DIR=$(dirname $0)
mkdir -p /var/log
LOG_FILE=/var/log/smi-initramfs.log
SERIAL_COMMAND="tee /dev/ttyS0"
if ! echo "." > /dev/ttyS0; then
  SERIAL_COMMAND="tee /dev/null"
  echo "Could not initialize serial port output" | tee -a ${LOG_FILE}
fi

$DIR/initialize 2>&1 | tee -a ${LOG_FILE} | ${SERIAL_COMMAND}
