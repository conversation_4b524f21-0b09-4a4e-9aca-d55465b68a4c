#!/bin/sh
echo "WORD UP"

set -e

SMI_INITRAMFS_DIR=/var/log/

SMI_INITRAMFS_LOG=${SMI_INITRAMFS_DIR}smi-initramfs.log

cloud_init_dir=/mnt/cloud-init
ovfenv_file="$cloud_init_dir/ovfenv.xml"

#Outputs to console and serial port for debugging purposes
output_message() {
   local message=$1
   local time=$(awk '{print $1}' /proc/uptime)
   echo "[smi-iso-init - $time] $message"
}
if [[ -f "/env" ]]; then
  output_message "Sourcing environment file"
  . /env
  env
fi
#Helps to allow running outside initramfs
if [[ -f /scripts/functions ]]; then
  . /scripts/functions
else
# Dummy versions of functions used so that script can be run outside initramfs
  panic() {
    output_message "$1"
    exit 1
  }
  configure_networking() {
    output_message "Fake network configuration..."
  }
fi
# Make sure virtual cdrom has been mounted properly
iso_device=$(blkid | grep ' LABEL="smi-install-iso"' | head -1 | cut -d':' -f1  || true)
retry_times=0
while [ -z "${iso_device}" ];
do
  #Load usb-storage module manually. Temporary workaround for the 5.4.0-49 kernel.
  if [[ -z "$(lsmod | grep usb_storage)" ]]; then
    output_message "Loading usb_storage module manually"
    
    #It appears at this point in the initramfs /lib/modules/<kernel version>/kernel is where modules are.  
    #Later it becomes /lib/modules/<kernel version>/<kernel version>/kernel not sure why.  We try both to deal with upgrades
    module_path="kernel/drivers/usb/storage/usb-storage.ko"
    /sbin/insmod /lib/modules/*/${module_path} || /sbin/insmod /lib/modules/*/*/${module_path}
    output_message "Return code: $?"
  fi
  output_message "Sleeping to wait for attachment of virtual drives"
  sleep 2
  iso_device=$(blkid | grep ' LABEL="smi-install-iso"' | head -1 | cut -d':' -f1  || true)
  retry_times=$(( $retry_times + 1 ))
  # Retry 10 times (20 seconds)
  if [ "$retry_times" -ge 10 ]; then
    panic "ERROR: Failed to get smi-install-iso virtual device after 10 retries."
    exit 1
  fi
done

setup_networking() {
  local dns=$1
  local url=$2
  output_message "Configuring networking"

  #Retry 3 times as it often fails at least once
  configure_networking || configure_networking || configure_networking
  output_message "Waiting for networking to be up..."
  #sleep 10
  
  case $url in
      *.nip.io*)
          nip_hostname=$(echo $url | sed -r 's|.*//(.*.nip.io).*|\1|g')
          nip_ip=$(echo $url| sed -r 's|([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+).nip.io.*|___\1|' | sed -r 's|.*___(.*)|\1|g')
          output_message "Adding: $nip_ip $nip_hostname to /etc/hosts"
          output_message "$nip_ip $nip_hostname" >> /etc/hosts
      ;;
  esac
  if [ -n "$dns" ]; then
    #NOTE: DNS is done after 'configure-networking' appears to run asynchronously and deletes /etc/resolv.conf
    #      There may be a better way to do this
    output_message "Setting DNS Server to: $dns"
    echo "nameserver $dns" >> /etc/resolv.conf
  fi

}
is_partitioning_needed() {

  return 0
}

#Put partition IDs and labels into variables to help keep things straight and make refactor possible
id_part_a="1"
id_part_b="2"
id_part_logs="3"
logs_label="smi-logs"
id_part_tmp="4"
tmp_label="smi-tmp"
id_part_state="5"
state_label="smi-state"
id_part_bios="14"
bios_label="bios"
id_part_efi="15"
efi_label="uefi"
id_part_cloud_config="12"
cloud_config_label="smi-cloud-config"
id_part_reserved="13"
smi_reserved="smi-reserved"

#Technically a smaller size would sort of function - but 40GB seems like a reasonable minimum.
#Not sure if that is workable for production though
min_disk_size_gb=40
min_size_bytes=$((${min_disk_size_gb}*1024*1024*1024 - 1)) #Minus one to ensure equal to 40GiB works

partition_drive() {
  local device=$1
  output_message "Partitioning hard drive device"

  #delete twice (we also delete in main command below as the first time old data can be converted from mbr
  sgdisk -Z ${device} || true
  
  output_message "Blank out the MBR just in case it exists"
  dd if=/dev/zero of=${device} seek=1 count=2047

  local total_disk_size_sectors=$(sgdisk -p ${device}| grep "Disk ${device}" | sed -r 's/.*: (.*) sectors.*/\1/g')
  output_message "Total Disk Sectors: $total_disk_size_sectors"
  local total_disk_bytes=$(( $total_disk_size_sectors * 512 ))
  
  local bios_mib=4
  local efi_mib=128

  local smi_reserved_mib=$((3072))
  local smi_reserved_bytes=$(( $smi_reserved_mib * 1024 * 1024))
  
  local smi_cloud_config_mib=$((32))
  local smi_cloud_config_bytes=$(( $smi_cloud_config_mib * 1024 * 1024))
  
  local boot_total_mib=$(($bios_mib + $efi_mib + $smi_reserved_mib + $smi_cloud_config_mib ))
  local boot_total_bytes=$(($boot_total_mib * 1024 * 1024))
  
  local partition_gib=10
  local partition_bytes=$(($partition_gib * 1024 * 1024 * 1024))
  local partition_total_bytes=$((2 * $partition_bytes))
  
  local total_overhead_bytes=$(( $partition_total_bytes + $boot_total_bytes + $smi_reserved_bytes + $smi_cloud_config_bytes ))
  local total_available_bytes=$(( $total_disk_bytes - $total_overhead_bytes ))
  local total_available_mib=$(( $total_available_bytes / 1024 / 1024 ))
  
  output_message "Total available MB after partition and bios overhead: ${total_available_mib}"
  
  local logs_percentage="0.1"
  local var_tmp_percentage="0.1"
  output_message "Total available bytes: ${total_available_bytes}"
  local stateful_percentage=$(echo "scale=10; 1.0-$logs_percentage-$var_tmp_percentage" | bc -l)
  
  local logs_mib=$(echo "scale=0; ($total_available_bytes * $logs_percentage)/1024/1024 " |bc -l)
  local var_tmp_mib=$(echo "scale=0; ($total_available_bytes * $var_tmp_percentage)/1024/1024" |bc -l)
  local stateful_mib=$(echo "scale=0; (($total_available_bytes * $stateful_percentage)/1024/1024) - 2" |bc -l)
  output_message "/var/logs (MiB): ${logs_mib} /var/tmp (MiB): ${var_tmp_mib}"
  output_message "/mnt/stateful_partition (MiB): ${stateful_mib} "
  
  output_message "Formatting disk"
  partition_command="sgdisk -Z -g ${device} \
         -n ${id_part_bios}:2048:+${bios_mib}MiB -t ${id_part_bios}:ef02 -c ${id_part_bios}:${bios_label} \
         -n ${id_part_efi}:0:+${efi_mib}MiB -t ${id_part_efi}:ef00 -c ${id_part_efi}:${efi_label} \
         -n ${id_part_cloud_config}:0:+${smi_cloud_config_mib}MiB -c ${id_part_cloud_config}:${cloud_config_label} \
         -n ${id_part_reserved}:0:+${smi_reserved_mib}MiB -c ${id_part_reserved}:${smi_reserved} \
         -n ${id_part_a}:0:+${partition_gib}GiB -c ${id_part_a}:smi-root-a"
  output_message "Partition command: $partition_command"
  $partition_command

  output_message "Refreshing partitions"
  partprobe
  output_message "Updated partitions:"
  lsblk

}
get_partition() {
  local device=$1
  local partition=$2
  
  #NOTE: grep is used to match a number at the end of device because
  #busybox (ash shell) does not support regex comparisions
  if echo "$device" | grep '[0-9]\+$' > /dev/null; then
    echo "${device}p${partition}"
  else
    echo "${device}${partition}"
  fi
}
format_partitions() {
  local device=$1
  output_message "Formatting: $(get_partition ${device} ${id_part_a})"
  mkfs.ext4 $(get_partition ${device} ${id_part_a})  -F -L "cloudimg-rootfs"
  output_message "Formatting: $(get_partition ${device} ${id_part_reserved})"

  mkfs.ext4 $(get_partition ${device} ${id_part_reserved})  -F -L "${smi_reserved}"
}

is_aws() {
  #"m" instances - c instances
  # smidecode -t system
  #  System Information
  #  	Manufacturer: Amazon EC2
  #  	Product Name: m5.large
  if dmidecode -t system | grep "Manufacturer" | grep "Amazon"; then
    return 0
  #t instances are run with Xen.  This is technically not AWS - but we don't deal with any other xen
  elif dmidecode -t system | grep "Product Name" | grep "HVM domU"; then
    return 0
  else
    return 1
  fi
}
is_openstack() {
  if dmidecode -t system | grep "Product Name" | grep  "OpenStack";  then
    return 0
  else
    return 1
  fi
}
is_vmware() {
  if dmidecode -t system | grep "Product Name" | grep -i "VMWare";  then
    return 0
  else
    return 1
  fi
}
is_ovf_env() {
  if vmtoolsd --cmd='info-get guestinfo.ovfenv';  then
    return 0
  else
    return 1
  fi
}

is_ovf_userdata() {
  output_message "Getting user data..."
  local userdata=$(get_ovf_property userdata)
  if [[ -n "${userdata}" ]]; then
    return 0
  fi
  return 1
}

ovf_env_to_cloud_init() {
  local userdata=$(get_ovf_property userdata | sed 's/\s//g')
  echo "${userdata}" > ${cloud_init_dir}/ovf-raw-user-data

  local decoded_userdata=""

  if echo "${userdata}" | base64 -d | gzip -t 2> /dev/null; then
    output_message "User data is gzipped"
    decoded_userdata="$(echo "${userdata}" | base64 -d | gzip -d)"
  else
    output_message "User data is not gzipped"
    decoded_userdata="$(echo "${userdata}" | base64 -d)"
  fi
  #output_message "Decoded userdata: ${decoded_userdata}"
  echo "${decoded_userdata}" > ${cloud_init_dir}/user-data
  output_message "Created: ${cloud_init_dir}/user-data"

  #meta-data file is required by cloud-init to be valid
  echo "instance-id: base-vm" > ${cloud_init_dir}/meta-data

}
get_ovf_property() {
  local key=$1
  if [[ ! -f "${ovfenv_file}" ]]; then
    vmtoolsd --cmd="info-get guestinfo.ovfenv" > ${ovfenv_file}
  fi
  echo $(xmlstarlet sel  -N oe="http://schemas.dmtf.org/ovf/environment/1" -t -v "/oe:Environment/oe:PropertySection/oe:Property[@oe:key='${key}']/@oe:value" ${ovfenv_file} || true)
}
initialize_cloud_init() {
  local device=$1
  
  if is_aws; then
    output_message "Running in AWS - not initializing cloud-init"
    return
  elif is_openstack; then
    output_message "Running in Openstack - not initializing cloud-init"
    return
  else
    output_message "No platform which can provide cloud-init user-data natively detected"
  fi
  output_message "Initializing cloud-init partition"

  iso_device=$(blkid | grep -v $(get_partition ${device} ${id_part_cloud_config}) | grep ' LABEL="cidata"' | head -1 | cut -d':' -f1  || true)

  mkfs.vfat -n cidata $(get_partition ${device} ${id_part_cloud_config})

  mkdir -p ${cloud_init_dir}
  mount $(get_partition ${device} ${id_part_cloud_config}) ${cloud_init_dir}

  if [[ -n "$iso_device" ]]; then
    output_message "Found cloud init ISO device (cidata): ${iso_device}"
    mkdir -p /mnt/cloud-init-iso
    mount ${iso_device} /mnt/cloud-init-iso
    cp -r /mnt/cloud-init-iso/* ${cloud_init_dir}/
    umount ${iso_device}
    eject ${iso_device}
    umount ${cloud_init_dir}
    return
  elif is_vmware && is_ovf_env && is_ovf_userdata; then
    output_message "Running in VMWare - and OVF provided user data - not initializing cloud-init"
    ovf_env_to_cloud_init
    return
  else
    output_message "No cidata cloud-init ISO found."
  fi

  if [[ -d "/smi/cloud-init" ]]; then
    output_message "Using fallback cloud-init data from /smi/cloud-init."
    cp -r /smi/cloud-init/* ${cloud_init_dir}/
  else
    output_message "No cloud-init files in /smi/cloud-init.  Cloud-init must be provided at boot time."
  fi
  output_message "Cloud init files: $(ls -la /smi/cloud-init || true)"
  umount ${cloud_init_dir}

}
initialize_boot() {
  local device=$1
  cat /smi/partition/efi.part.tar.xz | /bin/tar Jxvf - --to-stdout | dd bs=1M > $(get_partition ${device} ${id_part_efi})
}
download_hdd() {
  local download_url=$1
  local download_file=$2
  output_message "Downloading hard drive image (${download_url}) to ${download_file}"
  wget ${download_url} -O ${download_file}
  #wget http://10.0.2.2:8080/smi-base-image.part.tar.xz -O ${download_file}
}
initialize_hdd() {
  local hd_file=$1
  local device=$2
  local root_device=$3
  output_message "Writing hard drive to disk..."
  TEMP_MOUNT=/mnt/temp
  mkdir -p ${TEMP_MOUNT}
  mount ${device} "${TEMP_MOUNT}"

  cd ${TEMP_MOUNT}
  /bin/tar xf $hd_file --warning=no-timestamp 
  mount -o bind /dev ${TEMP_MOUNT}/dev
  mount -o bind /proc ${TEMP_MOUNT}/proc
  mount -o bind /sys ${TEMP_MOUNT}/sys
  
  output_message "Root device: ${root_device}"
  chroot . grub-install --target=i386-pc --skip-fs-probe  ${root_device}
  chroot . update-grub
  if [[ -d "/smi/root" ]]; then
    cp -r /smi/root/* ${TEMP_MOUNT}/
  fi
  cd -
  umount ${TEMP_MOUNT}/dev
  umount ${TEMP_MOUNT}/proc
  umount ${TEMP_MOUNT}/sys

  umount ${TEMP_MOUNT}
  output_message "Done writing disks"

}

backup_files() {
  device=$1
  cloud_init_dir=$2 
  partition_file=$3
  backup_dir=/mnt/backup

  echo "Backing up cloud-init and partition file into the smi-reserved partition"
  mkdir -p ${backup_dir}
  mount ${device} ${backup_dir}
  rm -rf ${backup_dir}/smi ${backup_dir}/cloud-init  #TODO: If we do upgrades, be smarter about old backups
  cp -r ${cloud_init_dir} ${backup_dir}
  cp -r /smi ${backup_dir}

  umount ${device}
  rm -rf ${backup_dir}
}

# Attempts to unmount/cleanup things so this script can run again and/or ensure logs are copied
# Will be run on any type of exit (error/normal)
# NOTE: Tries to not output extra error messages as it can be confusing when this function
#         is called on another error
cleanup() {

  output_message "Cleaning up..."

  umount ${TEMP_MOUNT} 2>/dev/null || true
 
  RESERVED_MOUNT=/reserved
  mkdir -p /reserved
  #Copy log into smi-reserved for troubleshooting
  mount $(blkid -L smi-reserved) "${RESERVED_MOUNT}" 2>/dev/null || true
  mkdir -p ${RESERVED_MOUNT}/log
  cp -f ${SMI_INITRAMFS_LOG} ${RESERVED_MOUNT}/log/smi-iso-init.log || true
  umount ${RESERVED_MOUNT} 2>/dev/null|| true
}

device_size_ok() {
  local root_drive="$1"
  local size_bytes=$(lsblk --bytes --nodeps --list ${root_drive} |  grep disk | tr -s ' ' | cut -d' ' -f4)
  #echo "device: ${device} size: ${size_bytes} min_size_bytes: ${min_size_bytes}"

  if [ $size_bytes -lt $min_size_bytes ]; then
    return 1
  else
    return 0
  fi

}
reset_other_drives() {

  if [[ "${IS_TEST}" ]]; then
     output_message "NOT RESETTING DRIVES.  IS_TEST: ${IS_TEST}"
     return
  fi
  local root_drive="$1"
  local drive_info="$(lsblk --bytes --nodeps --list | grep disk | cut -d' ' -f1)"
  
  for device in $drive_info; do
    output_message "reset drive: evaluating ${device}"
    if [ "$root_drive" == "/dev/$device" ]; then
      output_message "reset drive: /dev/${device} is root disk.  Skipping..."
    else
      output_message "reset drive - resetting: /dev/${device}..."
      output_message "sgdisk output: $(sgdisk -Z /dev/${device})"
      sgdisk -Z /dev/${device}
    fi
  done

}
is_scsi() {
  local device=$1
  bus=$(udevadm info ${device} | grep ID_BUS | sed 's/.*ID_BUS=//g')
  if [ "${bus}" == "scsi" ]; then
    return 0
  else
    return 1
  fi
}
get_hard_drive_device() {

  if [[ -n "$TEST_HARD_DRIVE_DEVICE" ]]; then
    echo "${TEST_HARD_DRIVE_DEVICE}"
    return
  fi
  local drive_info="$(lsblk --bytes --nodeps --list | grep disk | cut -d' ' -f1)"

  local found_device
  #echo "Drive info: $drive_info"
  for device in $drive_info; do
    if ! device_size_ok /dev/${device}; then
      continue
    elif is_scsi /dev/${device}; then
      echo "/dev/$device"
      return
    fi
  done

  for device in $drive_info; do
    if ! device_size_ok /dev/${device}; then
      continue
    else
      echo "/dev/$device"
      return
    fi
  done
}

print_debug_info() {

DEBUG_INFO=$(cat <<-EOM

Network Info (ip addr): 
------------------------
$(ip addr)

Disks - Mounted (df):
------------------------
$(df)

Disks - All (lsblk):
------------------------
$(lsblk)

Disk IDs (blkid):
------------------------
$(blkid)

Kernel Parameters (cat /proc/cmdline):
------------------------
$(cat /proc/cmdline)

System Information (dmidecode -t system):
------------------------
$(dmidecode -t system)

EOM
)

output_message "$DEBUG_INFO"

}

#Turn off "Current vnic speed set to..." messages.  
#  See: https://superuser.com/questions/351387/how-to-stop-kernel-messages-from-flooding-my-console/793692#793692 
#  These numbers may neeed to be further refined if needed kernel messages are missed - but scope should
#  be limited to this initramfs (till it writes to disk and reboots) so should be OK
output_message "Supressing low level kernel messages"
sysctl -w kernel.printk="2 4 1 7"

trap cleanup EXIT

print_debug_info

base_partition_file=/smi/base.part.tar.xz
if ! is_partitioning_needed; then
  output_message "No Partitioning needed"
  exit 0
fi

iso_device=$(blkid | grep ' LABEL="smi-install-iso"' | head -1 | cut -d':' -f1  || true)
if [[ -n "${iso_device}" ]]; then
  output_message "ISO device: ${iso_device}"
  TEMP_ISO_MOUNT=/mnt/tmp_iso
  SMI_ISO_DIR="${TEMP_ISO_MOUNT}/smi"
  mkdir -p ${TEMP_ISO_MOUNT}
  output_message "Mounting ISO to look for additional files"
  mount ${iso_device} ${TEMP_ISO_MOUNT}
  if [[ -d "${SMI_ISO_DIR}" ]]; then
    output_message "smi folder found - copying - this can be slow with virtual media"
    
    #Turn off error checking as we retry rsync copy until done
    #rsync makes retry more efficient
    set +e
    return_code=1
    partial_retry_count=3
    retry_count=3
    for j in {0..$retry_count}; do
      for i in {0..$partial_retry_count}; do
        output_message "Copying files from ${SMI_ISO_DIR} to root (/)"
        rsync -av --partial ${SMI_ISO_DIR}/ /smi
        return_code=$?
        if [[ $return_code -ne 0 ]]; then
          output_message "rsync not successful  - sleeping before retry"
          sleep 10
        elif [[ $return_code -eq 0 ]]; then
          output_message "rsync successful"
          break
        fi
      done
      if [[ $return_code -eq 0 ]]; then
        output_message "rsync successful"
        break
      else
        if [[ $i == "$retry_count" ]]; then
          output_message "rsync not successful - retries exhausted"
          output_message "Rebooting. Please check network related issues"
          shutdown -r now
        else
          output_message "rsync not successful - full retry"
          rm -rf /smi
          mkdir -p /smi
        fi
      fi
    done
    set -e
  fi
  output_message "Unmounting ISO"
  umount -f ${TEMP_ISO_MOUNT}
fi

#Setup networking first as it takes some time after finish to initialize and prevents unneeded 'sleep'
if [[ ! -f "${base_partition_file}" ]]; then
  output_message "Setting up networking as ${base_partition_file} needs to be downloaded from: ${base_image_url}"
  setup_networking $dns $base_image_url
fi

hard_drive_device=$(get_hard_drive_device)
if [[ -z "$hard_drive_device" ]]; then
  panic "ERROR: No useable hard drive device (minimum size: ${min_disk_size_gb}GiB) found!  Look at: lsblk | grep disk"
  exit 1
fi
output_message "Found hard drive: ${hard_drive_device}"

output_message "Resetting (set blank partition table) all drives that are not ${hard_drive_device}"
reset_other_drives ${hard_drive_device}

output_message "Partitioning disk: ${hard_drive_device}"
partition_drive ${hard_drive_device}
format_partitions ${hard_drive_device}
initialize_cloud_init ${hard_drive_device}
initialize_boot ${hard_drive_device}

#If we make a 'fat ISO' with the disk in it, this will make that work and avoid networking
if [[ ! -f "${base_partition_file}" ]]; then
  download_hdd ${base_image_url} ${base_partition_file}
fi 

initialize_hdd ${base_partition_file} $(get_partition ${hard_drive_device} ${id_part_a}) ${hard_drive_device}
#configure_hdd_files ${hard_drive_device}${id_part_a}
output_message "Backing up files to smi reserved partition: $(get_partition ${hard_drive_device} ${id_part_reserved}) (${cloud_init_dir} and ${base_partition_file})"
backup_files $(get_partition ${hard_drive_device} ${id_part_reserved}) ${cloud_init_dir} ${base_partition_file}

#This now shows how information looks after initializatioin
print_debug_info

cdrom_device=$(blkid | grep ' LABEL="smi-install-iso"' | head -1 | cut -d':' -f1 )

if [[ -n "${cdrom_device}" ]]; then
  output_message "Ejecting ISO cdrom (${cdrom_device})..."
  
  #NOTE: This randomly fails with "Unable to eject, last error: Invalid Argument"
  # if that failure can be addressed || true can be removed
  eject ${cdrom_device} || true
else
  output_message "ISO device not found"
fi

if [[ "$IS_TEST" == "true" ]]; then
   exit 0 #cleanup will be called on normal and we shouldn't reboot in chroot test
fi
#If we have made it this far cleanup must be called manually as next is forced reboot
cleanup

output_message "Rebooting..."
sync
echo b >/proc/sysrq-trigger

