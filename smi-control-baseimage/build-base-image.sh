#!/bin/bash
set -e
set -o pipefail
#CN image for Version 9.64 and onwards. CN products taken from master and Secure Proxy from 9.64 branch
DIR=$(cd $(dirname $0) && pwd)
CN_VER="2022.10.06.i01"
SP_VER="2022.09.22.i01"

BASE_DIR="${DIR}/"
TMP_DIR=/var/tmp/.vulcan  #Temp dir is set outside of 'vulcan' dir so it will be faster in docker bind mount case
mkdir -p ${TMP_DIR}

DOWNLOADS=$BASE_DIR/.vulcan/downloads
ANSIBLE_DIR=$BASE_DIR/ansible
PORTABLE_ANSIBLE_FILE=${DOWNLOADS}/ansible-portable.tar.bz2
HDD_IMAGE_TMP=${TMP_DIR}/hdd.raw
HDD_IMAGE_TMP_INITIAL=${TMP_DIR}/hdd-initial.raw

CLOUD_INIT_DIR=${DIR}/files/cloud-init
GRUB_CFG=${DIR}/files/grub/grub.cfg
SOURCES_LIST=${DIR}/files/apt/sources.list

NAME=inception-install
VM_DIR=./artifacts
mkdir -p ${VM_DIR}

PARTITION_TAR_NAME=${VM_DIR}/${NAME}.part.tar.xz
OUTPUT_ISO=${VM_DIR}/${NAME}.iso
OVA_FILE=${VM_DIR}/${NAME}.ova 

BASE_IMAGE_FILES=./base-image
CLUSTER_SYNC_FILES=${DIR}/shared-iso-files
SMI_INITRAMFS_NAME=${VM_DIR}/smi_initrd.img
SMI_INITRAMFS_BASE_NAME=${VM_DIR}/smi_base_initrd.img
KERNEL_FILE=${BASE_IMAGE_FILES}/vmlinuz-5.4.0-128-generic
INITIALIZE_SCRIPT_FROM="${CLUSTER_SYNC_FILES}/initialize"
INITIALIZE_SCRIPT_TO="/scripts/local-top/initialize"
CLOUD_INIT_FILES_FROM="./cloud-init"
CLOUD_INIT_FILES_TO="/smi/cloud-init"
INITIALIZE_SHIM_FROM="${CLUSTER_SYNC_FILES}/smi-shim"
INITIALIZE_SHIM_TO="/scripts/local-top/smi"

SMI_EFI_PARTITION_FROM="${BASE_IMAGE_FILES}/base-vm.efi.tar.xz"
SMI_EFI_PARTITION_TO="/smi/partition/efi.part.tar.xz"

USER_ID=901
USER_GROUP=901
DOCKER_GROUP=999

export BUILD_TAG=$(cat ${DIR}/component.properties | grep 'version:' | sed 's/.*-//g')
export BUILD_TIME=$(date)
export BUILD_VERSION=$(cat ${DIR}/component.properties | grep 'version:' | sed 's/.*: //g')

if [[ ! -f ${DIR}/tools/docker/bash/get-docker-image-name-functions ]]; then
  ${DIR}/init
fi
export VERSION=$(cat ${DIR}/component.properties | grep 'version:' | sed 's/.*: //g')
source ${DIR}/tools/docker/bash/get-docker-image-name-functions
export BUILD_FULL_VERSION=$(echo $(get_docker_image_name "smi-base-image" "") | sed 's/.*://g')
export BUILD_HOSTNAME=$(hostname)

echo "Build tag: ${BUILD_TAG}"
echo "Build time: ${BUILD_TIME}"
echo "Build version: ${BUILD_VERSION}"
echo "Build full version: ${BUILD_FULL_VERSION}"
echo "Build hostname: ${BUILD_HOSTNAME}"

if [[ $EUID -ne 0 ]]; then
   echo "This script must be run as root"
   exit 1
fi

INITRAMFS_BASE=${BASE_IMAGE_FILES}/initrd.img
#Base version = 20220927
BASE_IMAGE=dockerhub.cisco.com/smi-fuse-docker-internal/smi-libraries/smi-signed-base-image/master/smi-signed-base-image:1.0.0-0091-f7225bf
rm -rf ${BASE_IMAGE_FILES/}
echo "pulling..."
docker pull ${BASE_IMAGE}
echo "creating..."
docker_id=$(docker create ${BASE_IMAGE} ls)
docker cp ${docker_id}:/base-vm ${BASE_IMAGE_FILES}
docker rm ${docker_id}

#rm -rf ${CLUSTER_SYNC_FILES}
#CLUSTER_SYNC=dockerhub.cisco.com/smi-fuse-docker-internal/smi-apps/smi-cluster-deployer/master/cluster_synchronizer:1.1.0-817bd87
#docker_id=$(docker create ${CLUSTER_SYNC} ls)
#docker cp ${docker_id}:/opt/run/server/ansible/roles/shared-iso-files/templates/ ${CLUSTER_SYNC_FILES}
#docker rm ${docker_id} 

DOCKER_TAR="${BASE_IMAGE_FILES}/docker-images.tar"
TOOLS_IMAGE=dockerhub.cisco.com/smi-fuse-docker-internal/5gaas/smi-config/master/smi-config:1.0.1-0380-90a1c83
SHORT_IMAGE=smi-config:build-image
if [[ ! -f "${DOCKER_TAR}" ]]; then
  docker pull ${TOOLS_IMAGE}
  docker tag ${TOOLS_IMAGE} ${SHORT_IMAGE}
  docker save "${TOOLS_IMAGE}" "${SHORT_IMAGE}" > ${DOCKER_TAR}
fi

REGISTRY_TAR="${BASE_IMAGE_FILES}/registry-images.tar"
REGISTRY_IMAGE=registry:2.7.1
if [[ ! -f "${REGISTRY_TAR}" ]]; then
  docker pull ${REGISTRY_IMAGE}
  docker save "${REGISTRY_IMAGE}" > ${REGISTRY_TAR}
fi

ART_USER=smi-readonly.gen
ART_PASS=AKCp5ekHjovek3NLqcMwePLcpsYUmuYgApYWGP54sRaKM1qQV8omemPFaCFWxFxaMV8eF2FqK
CONTROL_TAR="${DIR}/files/offline_release/control-node-products-${CN_VER}.tar"
CONTROL_NODE_REPOSITORY="https://engci-maven-master.cisco.com/artifactory/smi-fuse-snapshot/dev/5gaas/control-node-products/${CN_VER}-offline/"
CONTROL_NODE_VERSION="control-node-products-${CN_VER}"
curl -f -u ${ART_USER}:${ART_PASS} -o ${CONTROL_TAR} ${CONTROL_NODE_REPOSITORY}${CONTROL_NODE_VERSION}.tar

SECURE_TAR="${DIR}/files/offline_release/secure-proxy-${SP_VER}.tar"
SECURE_PROXY_REPOSITORY="https://engci-maven-master.cisco.com/artifactory/smi-fuse-snapshot/dev/5gaas/secure-proxy-products/${SP_VER}-offline/"
SECURE_PROXY_VERSION="secure-proxy-${SP_VER}"
curl -f -u ${ART_USER}:${ART_PASS} -o ${SECURE_TAR} ${SECURE_PROXY_REPOSITORY}${SECURE_PROXY_VERSION}.tar

PORTABLE_ANSIBLE_URL=https://github.com/ownport/portable-ansible/releases/download/v0.4.1/portable-ansible-v0.4.1-py3.tar.bz2
PORTABLE_ANSIBLE_SHA256=7bfc5e57eb21b8086391d76a5842da5114d56200eabd019e4b70c3e52affd7b7

vulcan --log-level=DEBUG download ${PORTABLE_ANSIBLE_URL} --output-file ${PORTABLE_ANSIBLE_FILE} --sha-256 ${PORTABLE_ANSIBLE_SHA256}

if [[ ! -f "${PARTITION_TAR_NAME}" ]]; then
# Build 'shim' disk so that bios grub install (and creation of /boot/grub/i386-pc) works
# This is for partition upgrades to not need to run install/update grub
vulcan --log-level DEBUG build \
  hard-drive-image --size 8G ${HDD_IMAGE_TMP_INITIAL} \
  add-partition --id 14 --size +4MiB --begin 2048 --typecode ef02 \
  add-partition-grub-efi --id 15 --size +128MiB --label "UEFI" --grub-cfg ${GRUB_CFG} \
  add-partition --id 1 --size +7GiB --label smi-root-a \
  init-partition --id 1 --label=cloudimg-rootfs

#- Initialize root disk and add files to configure initramfs with kernel modules needed by SMI
#- Run ansible
vulcan --log-level DEBUG build disk-image ${HDD_IMAGE_TMP_INITIAL} \
   mount --partition 1 \
   add-archive ${BASE_IMAGE_FILES}/base-vm.part.tar.xz \
   mount-kernel-dirs \
   run-cmd "mv /etc/resolv.conf /etc/resolv.conf.bak" \
   add /etc/resolv.conf \
   add "files/apt.sources.list:/etc/apt/sources.list" \
   add "${DIR}/files/smi-initramfs/:/etc/initramfs-tools" \
   add "${DOCKER_TAR}:/docker.tar" \
   add "${REGISTRY_TAR}:/registry.tar" \
   run-cmd "mkdir -p ${USER_ID}:${USER_GROUP} /data/agent-manager/offline_release" \
   add "${CONTROL_TAR}:/data/agent-manager/offline_release/control-node-products-${CN_VER}.tar" \
   add "${SECURE_TAR}:/data/agent-manager/offline_release/secure-proxy-${SP_VER}.tar" \
   add "files/boot-scripts/init-control-node.sh:/data/control-node/init-control-node.sh" \
   run-cmd "chmod 755 /data/control-node/init-control-node.sh" \
   run-cmd "mkdir -p /data/agent-manager/config.d/" \
   add "files/smi/control-node/base-config.yaml:/data/agent-manager/config.d/base-config.yaml" \
   run-cmd "mkdir -p /data/agent-manager/inception/" \
   add "files/smi/control-node/container-reset:/data/agent-manager/inception/container-reset" \
   run-cmd "chmod 755 /data/agent-manager/inception/container-reset" \
   run-cmd "chown -R ${USER_ID}:${USER_GROUP} /data/agent-manager" \
   run-cmd "chmod -R 775 /data/agent-manager" \
   add "files/etc/profiles.d/smi-aliases.sh:/etc/profiles.d/smi-aliases.sh" \
   run-cmd "chown -R root:root /etc/profiles.d/smi-aliases.sh" \
   run-cmd "chmod 755 /etc/profiles.d/smi-aliases.sh" \
   add-archive --destination-dir /var/tmp "${PORTABLE_ANSIBLE_FILE}"  \
   run-cmd "mv /var/tmp/ansible /opt/ansible-playbook" \
   add "${ANSIBLE_DIR}/:/ansible" \
   add "./initialize:/initialize" \
   run-cmd "/initialize" \
   run-cmd "rm /docker.tar" \
   run-cmd "rm /registry.tar" \
   run-cmd "mv /etc/resolv.conf.bak /etc/resolv.conf" \
   run-cmd "rm /initialize" \
   rm "/opt/ansible-playbook" \
   rm "/ansible" \
   copy-from /initrd.img ${SMI_INITRAMFS_BASE_NAME} \
   create-archive --use-compress-program xz ${PARTITION_TAR_NAME}
   rm -rf ${HDD_IMAGE_TMP_INITIAL}
fi

vulcan --log-level DEBUG build initramfs --compression xz \
       ${SMI_INITRAMFS_NAME} \
       add ${INITIALIZE_SCRIPT_FROM}:${INITIALIZE_SCRIPT_TO} \
       add "${INITIALIZE_SHIM_FROM}:${INITIALIZE_SHIM_TO}" \
       add "${SMI_EFI_PARTITION_FROM}:${SMI_EFI_PARTITION_TO}" \
       add "${CLOUD_INIT_FILES_FROM}:${CLOUD_INIT_FILES_TO}" \
       add "${CLUSTER_SYNC_FILES}/base-image-initialize:/smi/root/etc/initramfs-tools/smi-init/initialize" \
       add "${CLUSTER_SYNC_FILES}/version_base_image:/smi/version_base_image" 

vulcan --log-level DEBUG build bootable-iso \
       --kernel ${KERNEL_FILE} \
       --initramfs ${SMI_INITRAMFS_BASE_NAME} \
       --initramfs ${SMI_INITRAMFS_NAME} \
       --add "${PARTITION_TAR_NAME}:/smi/base.part.tar.xz" \
       --kernel-args="console=ttyS0,115200 console=tty1" \
       -l DEBUG \
       --output-iso ${OUTPUT_ISO}

echo -e "Artifacts"
cat <<Endofmessage

Build Artifacts
-------------------------------------
Default ISO: ${OUTPUT_ISO}
Base image: ${PARTITION_TAR_NAME}
Kernel: ${KERNEL_FILE}
Initramfs: ${SMI_INITRAMFS_NAME}
-------------------------------------

Endofmessage
