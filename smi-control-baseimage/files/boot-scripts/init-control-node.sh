#!/bin/bash
set -e
set -o pipefail

# This will setup the bare minimum folder structure for the
# smi-config user (id: 901).  The actual user will be created
# via the 'init' container in agent-manager-client
USER_ID=901
USER_GROUP=901
DOCKER_GROUP=999
TMP_ENV_FILE=/tmp/env

mkdir -p /data/agent-manager
mkdir -p /data/agent-manager/inception/registry
mkdir -p /data/agent-manager/config.d
chown -R ${USER_ID}:${USER_GROUP} /data/agent-manager
chmod -R 775 /data/agent-manager

# wait for docker to be running
while true; do
  res=$(systemctl is-active docker >/dev/null 2>&1 && echo YES || echo NO)
  if [ "$res" == "NO" ]; then
    echo "Docker service is not yet active..."
    sleep 3
  else
    break
  fi
done

# Create docker environment file with *only* contents of /etc/environment
#   This is done by using a subshell to remove all variables, sourcing /etc/environment
#   and outputting all variables to the temp file.
#   The subshell is used to avoid changing the current scripts context
$(
  #unset all variables
  for i in $(env | awk -F"=" '{print $1}') ; do
  unset $i ; done

  #only get variables for /etc/environment
  source /etc/environment
  env > ${TMP_ENV_FILE}
)

while ! docker run --rm -v /var/run/docker.sock:/var/run/docker.sock -v /etc/:/etc/ --env-file=${TMP_ENV_FILE} --user ${USER_ID}:${DOCKER_GROUP} -v /data/agent-manager/:/data/agent-manager/ --network="host" smi-config:build-image smi-config agent apply --url file:///data/agent-manager/config.d/base-config.yaml --log-level INFO
do
	echo "Waiting for the base agents to be installed"
	sleep 6
done
