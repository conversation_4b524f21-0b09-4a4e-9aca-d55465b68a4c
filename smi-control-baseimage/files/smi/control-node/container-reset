#!/usr/bin/env python3

try:
    import logging
    import sys
    import os
    import re
    from shutil import which
    from subprocess import Pope<PERSON>, PIPE, STDOUT
    from multiprocessing import Pool as ThreadPool
except ImportError as e:
    raise ImportError(str(e) + """
        A critical module was not found.Please fix the import error.""")

logger = logging.getLogger(__name__)


def run_command(command, error_ok=False, show_progress=False, env=None):
    p = Popen(command, shell=True, stdin=None, stdout=PIPE, stderr=STDOUT, env=env)
    logging.debug("Running command: " + command)
    stdout = ""
    stdoutList = []
    while True:
        if p.stdout:
            output = p.stdout.readline().decode('utf-8')
        if output == '' and p.poll() is not None:
            break
        if output:
            str_output = output.rstrip()
            stdoutList.append(str_output)
            if show_progress:
                print(str_output)
    rc = p.poll()

    '''if stdout and not show_progress:
        logging.debug("Standard out: {}".format(stdout))'''
    logging.debug(f'Return code: {rc}')
    if rc != 0 and error_ok is False:
        logging.error(
            f"A fatal error occurred executing command '{command}'."
            f" Return code {rc}. Output:")
        for line in stdoutList:
            logging.error(line)
        raise SystemExit(1)

    stdout = "\n".join(stdoutList)
    return stdout, rc


def fetch_container():
    container_list = list()
    cmd = "docker ps -a | sed -n '1!p' | awk -F' ' '{print $NF}'"
    res, rcode = run_command(cmd, show_progress=True)
    if rcode != 0:
        logger.error("Failed to fecth all docker containers. Exiting ...")
        sys.exit(-1)
    for line in res.splitlines():
        container_list.append(line.strip())
    return container_list


def restart_container(container_list):
    if container_list.__len__()==0:
        return
    res_set = {run_command('docker restart '+item, show_progress=True) for item in container_list}
    fail_container = [ item[0] for item in res_set if item[1] != 0 ]
    if fail_container.__len__()==0:
        logger.info('All containers successfully restarted')
    else:
        for item in fail_container:
            logger.info('Failed restarting {}\n'.format(item))

def main():
    print("Fetching Containers ..............")
    container_list = fetch_container()
    print("\nRestarting Containers ............")
    restart_container(container_list)

if __name__ == "__main__":
    main()
