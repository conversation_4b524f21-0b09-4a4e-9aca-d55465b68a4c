version: "3.7"
services:
  agent_manager_client:
    image: "agent_manager_client"
    labels:
      deployment_version: initial
    network_mode: "host"
    restart: always
    init: true
    privileged: true
    user: "901:999"
    environment:
      - ROOT_DATA_DIR=/data/control-node/
      - LHM_DIR=/data/linux-device-manager/data
      - REGISTRY_HOST=127.0.0.1
      - REGISTRY_PORT=6000
      - SERVER_URL=http://127.0.0.1:10081
      - AGENT_LOG_LEVEL=INFO
      - SSL_VERIFY=False
      - ALLOW_UNSIGNED_IMAGES=true
    volumes:
      - "/data/control-node//:/data/control-node//"
      - type: bind
        source: /var/run/docker.sock
        target: /var/run/docker.sock
      - type: bind
        source: /var/run/dbus/system_bus_socket
        target: /var/run/dbus/system_bus_socket
      - type: bind
        source: /etc
        target: /host_etc
        read_only: true
      - type: bind
        source: /etc/systemd/system
        target: /etc/systemd/system
        read_only: true
      - type: bind
        source: /etc/smi/base-image-version
        target: /etc/smi/base-image-version
        read_only: true
      - "/data/linux-device-manager/cimc/:/data/linux-device-manager/cimc/"
      - "/run/systemd/system:/run/systemd/system"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "10"
        tag: "agent-manager-client"
        mode: "non-blocking"
        max-buffer-size: "4m"
  init_container:      
    image: "agent_manager_client_init"
    network_mode: "host"
    init: true
    privileged: true
    volumes:
      - "/:/host-root"
    command: "/install"
  download_manager:
    image: "agent_manager_client_download_manager"
    depends_on:
      init_container:
        condition: service_completed_successfully
    labels:
      deployment_version: initial
    network_mode: "host"
    restart: always
    init: true
    user: "901:999"
    env_file:
      - /etc/environment
    environment:
      - DOWNLOADS_LOCATION=/data/control-node/downloads/
    volumes:
      - "/data/control-node//:/data/control-node//"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "10"
        tag: "agent-manager-client"
        mode: "non-blocking"
        max-buffer-size: "4m"
