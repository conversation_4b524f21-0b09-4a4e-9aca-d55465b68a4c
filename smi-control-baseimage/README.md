# Summary
This is an example of using vulcan to build the SMI base image.  This should be soon merged into mainline to replace the VMWare specific process.

## Building - docker
Run: 
```build-base-image-docker.sh``` - NOTE: This uses privileged and mounts /dev...so it's possible it could mess up your system.

## Building - linux (root required)
- Get vulcan installed.  activating a python environment for it is fine.
- Run:
```./build-base-image.sh```

Artifacts will show up in 'base-vm' folder.
