ARG SMI_FROM_LATEST_MINOR=dockerhub.cisco.com/smi-fuse-docker-internal/smi-libraries/releases/ubuntu-base/22.04.4/ubuntu-base:22.04.4
#ARG SMI_FROM_LATEST_MINOR_GOLANG=dockerhub.cisco.com/smi-fuse-docker-internal/smi-libraries/releases/golang/1.23.8.1/golang:1.23.8.1

#FROM ${SMI_FROM_LATEST_MINOR_GOLANG} as go_builder

FROM dockerhub.cisco.com/smi-fuse-docker-internal/smi-libraries/releases/golang/1.22.12.2/golang:1.22.12.2 AS go_builder

ARG GITHUB_READONLY_USERNAME
ARG GITHUB_READONLY_TOKEN

ENV GOPRIVATE=wwwin-github.cisco.com \
    GOLANGCI_LINT_VERSION=v1.59.1 \
    REVIVE_VERSION=v1.3.7

COPY . ./lfsmgmt

WORKDIR /lfsmgmt

RUN echo "machine wwwin-github.cisco.com login $GITHUB_READONLY_USERNAME password $GITHUB_READONLY_TOKEN" > ~/.netrc \
    && make docker

RUN go install github.com/jstemmer/go-junit-report/v2@v2.1.0 && make coverage

RUN curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin ${GOLANGCI_LINT_VERSION} \
    && go install github.com/mgechev/revive@${REVIVE_VERSION} \
    && make lint

RUN go install github.com/securego/gosec/v2/cmd/gosec@v2.18.2 && make sec

RUN mkdir -p /lfsmgmt/reports \
    && cp cover.out /lfsmgmt/reports/ && cp report.xml /lfsmgmt/reports/ \
    && cp lint-report.xml /lfsmgmt/reports/ && cp sec-report.json /lfsmgmt/reports/
 
FROM ${SMI_FROM_LATEST_MINOR}

RUN apt-get-update && \
    apt-get install -y iputils-ping curl  tcpdump && \
    apt-cleanup

RUN groupadd -g 303 appuser && useradd -r -u 303 -g appuser appuser
ENV APP_USER=appuser
USER ${APP_USER}

COPY --from=go_builder --chown=${APP_USER} /lfsmgmt/lfsmgmt /usr/local/bin/lfsmgmt
COPY --from=go_builder --chown=${APP_USER} /lfsmgmt/payload /usr/local/bin/payload
COPY --from=go_builder --chown=${APP_USER} /lfsmgmt/values-dev.yaml /usr/local/bin/values-dev.yaml

WORKDIR /usr/local/bin
ENTRYPOINT ["./lfsmgmt"]
